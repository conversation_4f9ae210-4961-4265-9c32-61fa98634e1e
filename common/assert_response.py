import struct


def convert_bytes(byte_data, encoding='utf-8', double_prefix=b'\t', byte_order='<'):
    """字节字符串转换方法"""
    if byte_data.startswith(double_prefix) and len(byte_data) == len(double_prefix) + 8:
        for order in [byte_order, '>' if byte_order == '<' else '<']:
            try:
                value = struct.unpack(f'{order}d', byte_data[len(double_prefix):])[0]
                if isinstance(value, float) and -1e308 < value < 1e308 and not value == float('nan'):
                    return value
            except struct.error:
                continue
    try:
        return byte_data.decode(encoding)
    except UnicodeDecodeError:
        return None


def _format_error(failed_fields, request_params=None, server_info=None):
    """格式化错误消息"""
    msgs = []
    if request_params:
        msgs.append(f"请求参数: {request_params}")
    msgs.append("断言失败字段列表：")
    for fail in failed_fields:
        processing = f" ({fail['处理方式']})" if '处理方式' in fail else ""
        msgs.append(f"- {fail['字段']}{processing}: 新接口={fail['新接口']}, 基准接口={fail['基准接口']}")
    return "\n".join(msgs)

def assert_response_equal(response, new_response, fields, integer_fields=None, truncate_fields=None,
                         request_params=None, **kwargs):
    """比较两个响应对象中的指定字段"""
    integer_fields = integer_fields or ['PERatio']
    truncate_fields = truncate_fields or {'QuoteTime': 12}
    failed_fields = []

    for field in fields:
        val1, val2 = getattr(response, field), getattr(new_response, field)
        processing = "直接比较"

        if field in integer_fields:
            try:
                val1 = int(float(val1)) if val1 is not None else None
                val2 = int(float(val2)) if val2 is not None else None
                processing = "整数比较"
            except (ValueError, TypeError):
                pass
        elif field in truncate_fields:
            try:
                length = truncate_fields[field]
                val1 = str(val1)[:length] if val1 is not None else None
                val2 = str(val2)[:length] if val2 is not None else None
                processing = f"截断到{length}位"
            except (ValueError, TypeError):
                pass

        if val1 != val2:
            failed_fields.append({"字段": field, "新接口": val2, "基准接口": val1, "处理方式": processing})

    if failed_fields:
        raise AssertionError(_format_error(failed_fields, request_params))

def assert_response_equal_with_peratio(response, new_response, fields, request_params=None):
    """包含PERatio和QuoteTime字段的响应比较"""
    return assert_response_equal(response, new_response, fields,
                               integer_fields=['PERatio'], truncate_fields={'QuoteTime': 12},
                               request_params=request_params)

def assert_response_equal_with_quotetime(response, new_response, fields, quote_time_length=12, request_params=None):
    """包含时间字段的响应比较"""
    time_fields = ['QuoteTime', 'quoteTime', 'quotetime', 'jclTime', 'jcltime', 'JclTime']
    return assert_response_equal(response, new_response, fields,
                               truncate_fields={f: quote_time_length for f in time_fields},
                               request_params=request_params)

def assert_list_equal(response, new_response, fields, slice_length=5, decimal_places=None):
    """比较两个对象指定字段的前几个元素"""
    failed_fields = []
    for field in fields:
        try:
            slice1, slice2 = getattr(response, field)[:slice_length], getattr(new_response, field)[:slice_length]
            if decimal_places is not None:
                slice1, slice2 = [round(x, decimal_places) for x in slice1], [round(x, decimal_places) for x in slice2]
            if slice1 != slice2:
                failed_fields.append({"字段": field, "新接口": slice2, "基准接口": slice1})
        except Exception as e:
            failed_fields.append({"字段": field, "新接口": "无法获取", "基准接口": f"异常: {str(e)}"})
    if failed_fields:
        raise AssertionError(_format_error(failed_fields))

def assert_round_response(response, new_response, fields, decimal_places=None, divisor=None,
                         round_fields=None, request_params=None, **kwargs):
    """比较两个响应对象中的指定字段，支持浮点数精度配置"""
    failed_fields = []
    for field in fields:
        val1, val2 = getattr(response, field), getattr(new_response, field)

        if decimal_places and (round_fields is None or field in round_fields):
            try:
                if isinstance(val1, (int, float)):
                    val1 = round(val1, decimal_places)
                if isinstance(val2, (int, float)):
                    val2 = round(val2, decimal_places)
            except (TypeError, ValueError):
                pass

        if divisor:
            try:
                if isinstance(val1, (int, float)):
                    val1 = round(val1 / divisor, 1)
                if isinstance(val2, (int, float)):
                    val2 = round(val2 / divisor, 1)
            except (TypeError, ValueError, ZeroDivisionError):
                pass

        if val1 != val2:
            failed_fields.append({"字段": field, "新接口": val2, "基准接口": val1})

    if failed_fields:
        raise AssertionError(_format_error(failed_fields, request_params))

def assert_minute_data_equal(response, new_response, fields, request_params=None):
    """分时数据比较，保留2位小数"""
    return assert_round_response(response, new_response, fields, decimal_places=2,
                               round_fields=['preSettlePrice', 'openPrice', 'highPrice', 'nowPrice', 'lowPrice', 'amount'],
                               request_params=request_params)


def assert_kline_data_equal(response, new_response, fields, request_params=None, **kwargs):
    """K线数据比较，价格字段保留2位小数"""
    return assert_round_response(response, new_response, fields, decimal_places=2,
                               round_fields=['fOpen', 'fHigh', 'fLow', 'fClose', 'YClose', 'fAmount'],
                               request_params=request_params)


def assert_financial_data_equal(response, new_response, fields, request_params=None, **kwargs):
    """财务数据比较，转换为整数"""
    return assert_response_equal(response, new_response, fields,
                               integer_fields=['ActiveCapital', 'zgb'], request_params=request_params)

def assert_fieldids(response, new_response, fields, request_params=None, **kwargs):
    """比较响应对象字段，支持特殊fieldids的浮点数精度配置"""
    failed_fields = []
    special_fieldids = {37, 38, 39, 52}

    # 获取fieldid
    fieldid = None
    for obj in [response, new_response]:
        try:
            if hasattr(obj, 'fieldid'):
                fieldid = obj.fieldid
                break
            elif hasattr(obj, 'parent') and hasattr(obj.parent, 'fieldid'):
                fieldid = obj.parent.fieldid
                break
        except AttributeError:
            continue

    for field in fields:
        try:
            val1 = convert_bytes(bytes(getattr(response, field)))
            val2 = convert_bytes(bytes(getattr(new_response, field)))
        except (AttributeError, TypeError):
            val1 = val2 = None

        is_special = (fieldid in special_fieldids if fieldid else False) or (field == 'value')

        if (is_special and isinstance(val1, (float, int)) and isinstance(val2, (float, int)) and
            val1 is not None and val2 is not None):
            val1, val2 = round(float(val1), 6), round(float(val2), 6)
            if val1 != 0:
                ratio = val2 / val1
                if not (0.999999 <= ratio <= 1.000001 or abs(val2 - val1) < 0.1):
                    failed_fields.append({"字段": field, "新接口": val2, "基准接口": val1})
            elif val2 != 0:
                failed_fields.append({"字段": field, "新接口": val2, "基准接口": val1})
        elif val1 != val2:
            failed_fields.append({"字段": field, "新接口": val2, "基准接口": val1})

    if failed_fields:
        raise AssertionError(_format_error(failed_fields, request_params))

def assert_tolerance_equal(response, new_response, fields, tolerance=10):
    """比较响应对象字段，允许误差范围内视为相等"""
    failed_fields = []
    for field in fields:
        val1, val2 = getattr(response, field), getattr(new_response, field)
        if abs(val1 - val2) > tolerance:
            failed_fields.append({"字段": field, "新接口": val2, "基准接口": val1})
    if failed_fields:
        raise AssertionError(_format_error(failed_fields))

