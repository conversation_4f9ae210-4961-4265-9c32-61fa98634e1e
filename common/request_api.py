import socket
import struct
import zlib
from google.protobuf.any_pb2 import Any
from common.log_handle import <PERSON>g<PERSON>anager
from typing import Optional, List, Dict
import JCLBean_pb2


class JCLApi:
    """JCL API 封装类"""
    #host: str = "**************", port: int = 8899

    def __init__(self, host: str = "**************", port: int = 18899):
        """
        初始化 JCL API 客户端
        :param host: 服务器地址
        :param port: 服务器端口
        """
        self.host = host
        self.port = port
        self.socket = None
        self.VERSION = 13  # 协议版本
        self.log = LogManager()

    def connect(self) -> bool:
        """
        连接到服务器
        :return: 是否连接成功
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))

            return True
        except Exception as e:
            self.log.log_error(f"连接服务器失败: {e}")
            return False

    def close(self):
        """关闭连接"""
        if self.socket:
            self.socket.close()
            self.socket = None
            self.log.log_info("\n连接已关闭")

    def _pack_header(self, data_len: int, req_no: int = 0, raw_len: int = 0, compressed: int = 0) -> bytes:
        """
        打包请求头
        :param data_len: 数据长度
        :param req_no: 请求功能号
        :param raw_len: 原始数据长度
        :param compressed: 压缩方式
        :return: 打包后的请求头
        """
        header = struct.pack('<IBBBBIIIIIc',
                             0,  # dwcrc
                             self.VERSION,  # 版本
                             compressed,  # 压缩方式
                             0,  # 加密方式
                             1,  # 协议类型（1 表示 Proto Buffer）
                             0,  # cookie
                             req_no,  # 请求号
                             0,  # 组 ID
                             data_len,  # 数据包长度
                             raw_len or data_len,  # 原始数据长度
                             b'\x00'  # 优先级
                             )

        return header

    def _send_request(self, req_no: int, req_data: Any = None) -> Optional[JCLBean_pb2.report_package]:
        """
        发送请求并接收响应
        :param req_no: 请求功能号
        :param req_data: 请求数据
        :return: 响应数据包
        """
        try:
            # 构建 report_package
            package = JCLBean_pb2.report_package()
            package.Version = self.VERSION
            package.req = req_no

            if req_data:
                any_msg = Any()
                any_msg.Pack(req_data)
                package.packdata.CopyFrom(any_msg)

            # 序列化数据
            data = package.SerializeToString()



            # 打包头部
            header = self._pack_header(len(data), req_no)

            # 发送数据
            self.socket.sendall(header + data)

            # 接收响应头
            resp_header = self.socket.recv(37)  # 响应头大小
            if not resp_header:
                return None

            # 解析响应头
            resp_header_data = struct.unpack('<IiIIBBBBIIIcI', resp_header)
            packet_len = resp_header_data[2]
            compressed = resp_header_data[5]


            # 接收响应数据
            resp_data = b''
            while len(resp_data) < packet_len:
                chunk = self.socket.recv(packet_len - len(resp_data))
                if not chunk:
                    break
                resp_data += chunk


            # 处理压缩
            if compressed == 1:  # zlib 压缩
                import zlib
                try:
                    resp_data = zlib.decompress(resp_data)

                    self.log.log_debug("使用 zlib 解压成功")
                except Exception as e:
                    self.log.log_error(f"zlib 解压失败: {e}")
            elif compressed == 2:  # snappy 压缩
                try:
                    import snappy
                    resp_data = snappy.decompress(resp_data)

                    self.log.log_debug("使用 snappy 解压成功")
                except ImportError:
                    self.log.log_error("未安装 snappy 库，无法解压")
                except Exception as e:
                    self.log.log_error(f"snappy 解压失败: {e}")

            # 解析响应数据
            resp_package = JCLBean_pb2.report_package()
            try:
                resp_package.ParseFromString(resp_data)


                if resp_package.packdata:
                    self.log.log_debug(f"响应数据类型: {resp_package.packdata.type_url}")

                return resp_package
            except Exception as e:
                self.log.log_error(f"解析响应包失败: {e}")
                return None

        except Exception as e:
            self.log.log_error(f"请求失败:请求功能号-{req_no} ,{e}")
            return None

    def _send_and_parse_request(self, req_no: int, req_data: Any = None, response_type: Any = None) -> Optional[Any]:
        """
        发送请求并解析响应的通用方法
        :param req_no: 请求功能号
        :param req_data: 请求数据
        :param response_type: 期望的响应数据类型
        :return: 解析后的响应数据
        """
        try:

            # 发送请求并接收响应
            response = self._send_request(req_no, req_data)
            if not response or not response.packdata:
                self.log.log_error("响应数据为空")
                return None

            # 解包响应数据
            self.log.log_info(f"请求成功:接口协议号-{req_no}")
            response_obj = response_type()
            if response.packdata.Unpack(response_obj):

                return response_obj
            else:
                response_obj.ParseFromString(response.packdata.value)

                self.log.log_debug(f"直接解析响应数据: {response_obj}")
                return response_obj
        except Exception as e:
            self.log.log_error(f"请求失败: {e}")
            return None

    def get_monitor_info(self) -> Optional[Dict]:
        """
        获取监控信息
        :return: 监控信息字典
        """
        monitor_ans = self._send_and_parse_request(JCLBean_pb2.report_reqno.MONITOR_REQ,
                                                   response_type=JCLBean_pb2.monitorinfo_ans)
        if not monitor_ans:
            return None
        return monitor_ans

    def get_code_info(self, setcode: int, startxh: int) -> Optional[List[Dict]]:
        """
        获取代码信息
        :param setcode: 市场代码
        :param startxh: 起始序号
        :return: 代码信息列表
        """
        req = JCLBean_pb2.code_req(setcode=setcode, startxh=startxh)
        code_ans = self._send_and_parse_request(JCLBean_pb2.report_reqno.CODE_NEW_REQ, req_data=req,
                                                response_type=JCLBean_pb2.code_ans)
        if not code_ans:
            return None

        return code_ans

    def subscribe_quote(self, nkeys: List[int]):
        """
        订阅或取消订阅指定品种的行情数据
        :param nkeys: 包含需要订阅的品种的唯一标识符列表；-1表示取消所有订阅
        :return: 订阅的行情数据列表
        """
        req = JCLBean_pb2.sub_unsub_hq_req()
        req.aKey.extend(nkeys)
        # 构建请求数据

        # 发送请求并解析返回数据
        response = self._send_and_parse_request(
            JCLBean_pb2.report_reqno.PUSH_HQ_SUB,
            req_data=req,
            response_type=JCLBean_pb2.multi_hq_ans
        )

        # 如果返回数据为空，则返回 None
        if not response:
            print("subscribe_hq返回数据为空")
            return None

        # 返回行情数据列表
        return response

    def unsubscribe_hq(self, aKey: List[int]) -> Optional[int]:
        """
        取消行情推送订阅
        :param aKey: 需要取消订阅的品种唯一Key列表，-1表示取消全部订阅
        :return: 剩余订阅数量
        """
        # 构造请求数据
        req = JCLBean_pb2.sub_unsub_hq_req(aKey=aKey)

        # 发送请求并解析返回数据
        req_no = JCLBean_pb2.report_reqno.PUSH_HQ_UNSUB # PUSH_HQ_UNSUB 的请求号

        unsub_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.unsub_hq_ans
        )

        # 如果返回数据为空，则返回 None
        if not unsub_ans:
            return None
        else:
            print(f"取消订阅的数据：{unsub_ans}")

        # 返回剩余订阅数量
        return unsub_ans

    def get_minute_req(self, code: str, setcode: int = 0, days: int = 1):
        """
        获取分时走势数据
        :param code: 证券代码
        :param setcode: 市场代码
        :param days: 获取的天数，1-10日分时走势
        :return: 分时走势数据
        """
        # 构造请求数据
        tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(code=code, setcode=setcode, nkey=0)
        req = JCLBean_pb2.minute_req(code=tag_code_with_nkey, days=days)

        # 发送请求并解析返回数据
        req_no = JCLBean_pb2.report_reqno.MINUTE_REQ  # 需要传入的请求编号

        minute_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.minute_ans
        )

        # 如果返回数据为空，则返回 None
        if not minute_ans:
            return None

        # 返回分时数据列表
        return minute_ans

    def get_adjusted_kline(self, code: str = "", setcode: int = 0, nkey: int = 0, period: int = 0, offset: int = 0,
                          num: int = 0, mulnum: int = 0) -> Optional[List[Dict]]:
        """
        获取复权K线数据
        :param code: 证券代码 (当 nkey 为 0 时使用)
        :param setcode: 市场代码 (当 nkey 为 0 时使用)
        :param nkey: 品种内部编码 (优先使用, 若非 0 则忽略 code 和 setcode)
        :param period: K线周期
        :param offset: 数据偏移量
        :param num: 请求的数据条数
        :param mulnum: 多维度数量
        :return: K线数据列表 或 None
        """
        # 构造 tagCodeWithNkey 子结构
        tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(
            nkey=nkey,
            setcode=setcode if nkey == 0 else 0,  # 如果提供了 nkey，则忽略 setcode
            code=code if nkey == 0 else ""  # 如果提供了 nkey，则忽略 code
        )

        # 构造请求数据
        req = JCLBean_pb2.analy_tq_req(
            code=tag_code_with_nkey,
            period=period,
            offset=offset,
            num=num,
            mulnum=mulnum
        )

        # 发送请求并解析返回数据
        req_no = 1370  # ANALY_TQ_REQ 的请求编号

        analy_tq_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.analy_tq_ans
        )

        # 如果返回数据为空，则返回 None
        if not analy_tq_ans:
            return None

        # 返回 K线数据列表 (analy_tq_ans.aK)
        return analy_tq_ans

    def get_sort_ex_hq(self, setDomain: int, coltype: int, startxh: int, wantnum: int,
                   sorttype: int, drate: int, fieldids: List[int]) -> Optional[List[Dict]]:
        """
        请求分类排序行情
        :param setDomain: 市场域
        :param coltype: 列类型
        :param startxh: 起始序号
        :param wantnum: 请求的条数
        :param sorttype: 排序类型
        :param drate: 数据更新速率
        :param fieldids: 要查询的字段ID列表
        :return: 分类排序行情响应数据
        """
        # 构建请求数据
        req = JCLBean_pb2.sort_ex_hq_req(
            setDomain=setDomain,
            coltype=coltype,
            startxh=startxh,
            wantnum=wantnum,
            sorttype=sorttype,
            drate=drate
        )
        req.fieldids.extend(fieldids)  # 添加字段ID列表

        # 发送请求并解析返回数据
        req_no = JCLBean_pb2.report_reqno.SORT_NOHQ_EX_REQ  # 请求编号
        sort_ex_hq_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.sort_ex_hq_ans
        )

        # 如果返回数据为空，则返回 None
        if not sort_ex_hq_ans:
            return None

        return sort_ex_hq_ans

    def get_sorted_hq(self, setDomain: int = 1, coltype: int = 2, startxh: int = 0,
                      wantnum: int = 10, sorttype: int = 0, drate: int = 0,
                      fieldids: List[int] = []) :
        """
        获取分类排序行情信息
        :param setDomain: 市场领域
        :param coltype: 排序列类型
        :param startxh: 起始序号
        :param wantnum: 请求数量
        :param sorttype: 排序类型
        :param drate: 筛选条件
        :param fieldids: 字段ID列表
        :return: 排序行情信息列表
        """
        # 构造请求数据
        req = JCLBean_pb2.sort_ex_hq_req(
            setDomain=setDomain,
            coltype=coltype,
            startxh=startxh,
            wantnum=wantnum,
            sorttype=sorttype,
            drate=drate,
            fieldids=fieldids
        )

        # 发送请求并解析返回数据
        req_no = JCLBean_pb2.report_reqno.SORT_EX_HQ_REQ  # 请求编号

        sorted_market_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.sort_ex_hq_ans
        )

        if not sorted_market_ans:
            return None

        return sorted_market_ans

    def get_market_config(self, codes: List[Dict[str, int]]):
        """
        获取财务数据
        :param codes: 证券代码和市场代码的列表
        :return: 财务数据列表
        """
        # 构造请求数据
        tag_codes_with_nkey = [
            JCLBean_pb2.tagCodeWithNkey(code=code['code'], setcode=code['setcode'], nkey=code['nkey'])
            for code in codes
        ]
        req = JCLBean_pb2.autobase_req(codes=tag_codes_with_nkey)

        # 发送请求并解析返回数据
        autobase_ans = self._send_and_parse_request(
            JCLBean_pb2.report_reqno.AUTOBASE_REQ,
            req_data=req,
            response_type=JCLBean_pb2.autobase_ans
        )

        # 如果返回数据为空，则返回 None
        if not autobase_ans:
            return None

        # 返回财务数据列表
        return autobase_ans.basep

    def get_equity_adjustments(self, codes: List[Dict[str, int]]):
        """
        获取股本除权数据
        :param codes: 每个证券的标识信息，包含 nkey、setcode 和 code
        :return: 股本除权数据列表
        """
        # 构造请求数据
        tag_codes = []
        for code_info in codes:
            tag_code = JCLBean_pb2.tagCodeWithNkey()
            tag_code.nkey = code_info.get('nkey', 0)  # 如果没有提供nkey, 默认是0
            tag_code.setcode = code_info.get('setcode', 0)  # 默认市场代码为0
            tag_code.code = code_info.get('code', '')  # 默认证券代码为空
            tag_codes.append(tag_code)

        req = JCLBean_pb2.autogbbq_req(codes=tag_codes)

        # 发送请求并解析返回数据
        req_no = JCLBean_pb2.report_reqno.AUTOGBBQ_REQ  # 请求编号

        autogbbq_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.autogbbq_ans
        )

        # 如果返回数据为空，则返回 None
        if not autogbbq_ans:
            return None

        return autogbbq_ans

        #31 市场信息
    def get_hostmore_req(self, setcode: int = 0, verflag: int = 2):
        """
        获取市场配置（HOSTMORE_REQ 请求）
        :param setcode: 市场代码
        :param verflag: 版本标识
        :return: 返回的主机配置信息列表
        """
        # 构造请求数据
        req = JCLBean_pb2.hostmore_req(setcode=setcode, verflag=verflag)

        # 发送请求并解析返回数据
        req_no = JCLBean_pb2.report_reqno.HOSTMORE_REQ  # 对应的请求编号

        hostmore_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.hostmore_ans
        )

        if not hostmore_ans:
            return None

        return hostmore_ans

    def get_tick_data(self, code: str = "600000", setcode: int = 0, startxh: int = 0, num: int = 10, type: int = 0):
        """
        获取 Tick 数据
        :param code: 证券代码
        :param setcode: 市场代码
        :param startxh: 起始序号
        :param num: 请求的数据条数
        :param type: 请求类型
        :return: Tick 数据列表
        """
        # 构造请求数据
        tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(code=code, setcode=setcode, nkey=0)  # 使用内部编码或代码
        req = JCLBean_pb2.tick_req(code=tag_code_with_nkey, type=type, startxh=startxh, num=num)

        # 发送请求并解析返回数据
        req_no = 4069  # 需要传入的请求编号 (TICK_REQ)

        tick_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.tick_ans
        )

        # 如果返回数据为空，则返回 None
        if not tick_ans:
            return None


        return tick_ans

    def get_auction_data(self, code: str = "600000", setcode: int = 0, nkey: int = 0, type: int = 1) -> Optional[
        List[Dict]]:
        """
        获取集合竞价带未匹配量数据
        :param code: 证券代码
        :param setcode: 市场代码
        :param nkey: 品种内部编码（如果提供，优先识别内部编码）
        :param auction_type: 集合竞价类型（1: 开盘集合竞价，4: 尾盘集合竞价）
        :return: 集合竞价数据列表，包含匹配量和未匹配量等
        """
        try:
            # 构造请求数据
            tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(code=code, setcode=setcode, nkey=nkey)
            req = JCLBean_pb2.auctiondata_req(code=tag_code_with_nkey, type=type)

            # 发送请求并解析返回数据
            req_no = 10029  # 对应的请求编号 AUCTIONDATA_REQ

            # 发送请求并接收解析后的响应
            auction_data_ans = self._send_and_parse_request(
                req_no,
                req_data=req,
                response_type=JCLBean_pb2.auctiondata_ans
            )

            # 如果返回数据为空，则返回 None
            if not auction_data_ans:
                return None

            return auction_data_ans

        except Exception as e:
            self.log.log_error(f"获取集合竞价数据失败: {e}")
            return None

    def get_block_sort_hq(self, code: str, setcode: int, coltype: int, startxh: int, wantnum: int, sorttype: int,
                          fieldids: List[int]) -> Optional[JCLBean_pb2.sort_ex_hq_ans]:
        """
        获取板块或指数成分股的排序行情数据

        Args:
            code (str): 板块或指数代码（例如 "000001"）
            setcode (int): 市场代码（例如 1）
            coltype (int): 列类型
            startxh (int): 起始序号（例如 0）
            wantnum (int): 请求的记录数（例如 10）
            sorttype (int): 排序类型（例如 1 表示升序）
            fieldids (List[int]): 请求的字段 ID 列表（例如 [1, 2, 3]）

        Returns:
            Optional[JCLBean_pb2.sort_ex_hq_ans]: 原始的 sort_ex_hq_ans 数据对象，或 None（如果请求失败）
        """
        # 检查是否已连接
        if not self.socket:
            self.log.log_error("未连接到服务器，请先调用 connect()")
            return None

        # 构造请求数据
        tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(code=code, setcode=setcode, nkey=0)
        req = JCLBean_pb2.sort_block_hq_req(
            code=tag_code_with_nkey,
            coltype=coltype,
            startxh=startxh,
            wantnum=wantnum,
            sorttype=sorttype,
            fieldids=fieldids  # repeated 字段直接赋值列表
        )

        # 发送请求并解析返回数据
        req_no = 2008  # BLOCK_SORT_HQ_REQ 的请求编号
        sort_ex_hq_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.sort_ex_hq_ans
        )

        # 如果返回数据为空，则返回 None
        if not sort_ex_hq_ans:
            self.log.log_error("获取板块排序行情数据失败，返回为空")
            return None

        # 直接返回原始 sort_ex_hq_ans 对象，不做处理
        return sort_ex_hq_ans



    def get_moneyflow_req(self, code: str = "600000", setcode: int = 0, offset: int = 0, num: int = 10):
        """
        获取资金流向数据
        :param code: 证券代码
        :param setcode: 市场代码
        :param offset: 数据偏移量
        :param num: 获取的数量
        :return: 资金流向数据
        """
        # 构造请求数据
        tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(code=code, setcode=setcode, nkey=0)
        req = JCLBean_pb2.moneyflow_req(code=tag_code_with_nkey, offset=offset, num=num)

        # 发送请求并解析返回数据
        req_no = JCLBean_pb2.report_reqno.HIS_MONEYFLOW_REQ  # 需要传入的请求编号

        moneyflow_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.moneyflow_ans
        )

        # 如果返回数据为空，则返回 None
        if not moneyflow_ans:
            return None

        # 返回资金流向数据（amt、vol、cnt 三个数据字段）
        return moneyflow_ans

    def get_block_xml(self) -> Optional[bytes]:
        """
        获取 block.xml 数据
        :return: block.xml 的二进制数据，如果请求失败则返回 None
        """
        # 请求编号
        req_no = 1360  # COMBBLOCK_NREQ

        # 发送请求并解析返回数据，无需请求参数
        block_ans = self._send_and_parse_request(
            req_no,
            req_data=None,  # 无请求数据
            response_type=JCLBean_pb2.Binary_Data_Ans
        )

        # 如果返回数据为空，则返回 None
        if not block_ans:

            return None
        decompressed_data = block_ans.data
        decompressed = zlib.decompress(decompressed_data)
        response = decompressed.decode('gbk')


        return response


    def get_sort_shq_ex_req(self,
                      keys: List[int] = None,
                      szkeys: List[str] = None,
                      fieldids: List[int] = None) -> Optional[JCLBean_pb2.sort_ex_hq_ans]:
        """
        获取排序行情数据 (功能号 2023)

        :param keys: 标的物数字ID列表 (与 szkeys 二选一)
        :param szkeys: 标的物代码列表 (与 keys 二选一)
        :param field_ids: 必填，需要查询的行情字段ID列表
        :return: sort_ex_hq_ans 对象或 None
        """
        # 参数校验
        if not (keys or szkeys):

            return None
        if keys and szkeys:

            return None
        if not fieldids:

            return None

        try:
            # 构建请求对象
            req = JCLBean_pb2.sort_shq_ex_req()

            # 填充标的物标识
            if keys:
                req.keys.extend(keys)  # 使用 extend 填充 repeated 字段
            else:
                req.szkeys.extend(szkeys)

            # 填充行情字段
            req.fieldids.extend(fieldids)

            # 发送请求并解析响应
            return self._send_and_parse_request(
                req_no=2023,  # SORT_NOSHQ_EX_REQ
                req_data=req,
                response_type=JCLBean_pb2.sort_ex_hq_ans
            )
        except Exception as e:

            return None

    def get_hqfd_req(self) -> Optional[JCLBean_pb2.CalcStatics_Agfd_AnsInfo]:
        """
        获取行情涨幅分段信息
        :return: 行情涨幅分段信息
        """
        # 发送请求并解析返回数据
        req_no = JCLBean_pb2.report_reqno.HQFD_REQ
        hqfd_ans = self._send_and_parse_request(
            req_no,
            response_type=JCLBean_pb2.CalcStatics_Agfd_AnsInfo
        )
        return hqfd_ans


    def get_sort_stock_block(self, code: str = "", setcode: int = 0, nkey: int = 0) -> Optional[
        List[JCLBean_pb2.stock_block_fields]]:
        """
        获取股票板块排序数据
        :param code: 证券代码（当 nkey 为 0 时使用）
        :param setcode: 市场代码
        :param nkey: 品种内部编码（优先级高于 code，设为 0 时使用 code）
        :return: 股票板块排序数据列表，或 None（如果请求失败）
        """
        # 构造嵌套的 tagCodeWithNkey 子结构
        tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(
            nkey=nkey,
            setcode=setcode,
            code=code if nkey == 0 else ""  # 当 nkey 不为 0 时，code 置空
        )

        # 因为接口文档未明确定义独立的请求消息，直接使用 tagCodeWithNkey 作为请求数据
        # 如果需要独立的请求消息，可以根据实际情况调整
        req = tag_code_with_nkey

        # 发送请求并解析返回数据
        req_no = JCLBean_pb2.report_reqno.SORT_STOCK_BLOCK_REQ  # 请求功能号 2019
        sort_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.sort_stock_block_ans
        )

        # 如果返回数据为空，则返回 None
        if not sort_ans:
            self._log('error', "获取股票板块排序数据失败")
            return None

        # 返回股票板块数据列表
        return sort_ans.fields

    def get_today_moneyflow(self, nkeys: List[int]) -> Optional[JCLBean_pb2.multi_today_moneyflow_ans]:
        """
        获取多标的当日资金流向数据
        :param nkeys: 标的物ID列表（长整型列表）
        :return: multi_today_moneyflow_ans 响应对象
        """
        try:
            # 构建请求数据
            req = JCLBean_pb2.multi_today_moneyflow_req()
            req.nkeys.extend(nkeys)  # 注意这里使用extend处理列表

            # 发送请求并解析响应
            return self._send_and_parse_request(
                req_no=JCLBean_pb2.report_reqno.SRV_STATICTIS_MILTI_MONEY_DAY_REQ,
                req_data=req,
                response_type=JCLBean_pb2.multi_today_moneyflow_ans
            )
        except Exception as e:
            print(e)

    def get_code_list(self, setcode: int, startxh: int) -> Optional[JCLBean_pb2.code_ans]:
        """
        1110 获取市场代码链数据（含中英文名称）

        :param setcode: 市场代码（取值范围参考交易所编码规范）
        :param startxh: 起始序号（0表示从头获取）
        :return: code_ans 包含StkInfoNew结构的响应对象
        """
        try:
            # 构建请求对象
            req = JCLBean_pb2.code_req()
            req.setcode = setcode  # uint32直接赋值
            req.startxh = startxh  # uint32直接赋值

            # 发送协议请求
            return self._send_and_parse_request(
                req_no=JCLBean_pb2.report_reqno.CODE6_NREQ,  # 根据实际枚举值调整
                req_data=req,
                response_type=JCLBean_pb2.code_ans
            )
        except Exception as e:
            print(e)
            return None

    def get_money_flow_day(self, code: str = "600000", setcode: int = 0, offset: int = 0, num: int = 10) -> Optional[Dict]:
        """
        获取日周期资金流向统计数据
        :param code: 证券代码
        :param setcode: 市场代码
        :param offset: 数据偏移量，从第几条数据开始
        :param num: 请求的数据条数
        :return: 包含资金流向统计数据的字典，或 None（如果请求失败）
        """
        # 构造请求数据
        tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(code=code, setcode=setcode, nkey=0)
        req = JCLBean_pb2.moneyflow_req(
            code=tag_code_with_nkey,
            offset=offset,
            num=num
        )

        # 发送请求并解析返回数据
        req_no = 23201  # SRV_STATICTIS_MONEY_DAY_REQ

        moneyflow_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.moneyflow_ans
        )

        # 如果返回数据为空，则返回 None
        if not moneyflow_ans:
            return None

        return moneyflow_ans

    def get_multi_hq(self, codes: List[Dict[str, Any]]) -> Optional[JCLBean_pb2.multi_hq_ans]:
        """
        请求指定若干品种的行情数据
        :param codes: 包含证券信息的列表，每个元素是一个字典，例如 [{"code": "600000", "setcode": 0, "nkey": 0}, ...]
        :return: multi_hq_ans 对象，或 None（如果请求失败）
        """
        # 构造请求数据
        req = JCLBean_pb2.multi_hq_req()
        for code_info in codes:
            tag_code = req.codes.add()
            tag_code.nkey = code_info.get("nkey", 0)
            tag_code.setcode = code_info.get("setcode", 0)
            tag_code.code = code_info.get("code", "")

        # 发送请求并解析返回数据
        req_no = 1728  # MULTI_HQ_REQ

        multi_hq_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.multi_hq_ans
        )

        return multi_hq_ans


    def get_his_minute_data(self, code: str = "600000", setcode: int = 0, date: int = 0) -> Optional[List[JCLBean_pb2.MinuteInfo]]:
        """
        获取历史分时走势数据
        :param code: 证券代码
        :param setcode: 市场代码
        :param date: 查询日期（格式如 20250314，若为 0 则使用默认值由服务器决定）
        :return: 历史分时数据列表
        """
        # 构造请求数据
        tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(code=code, setcode=setcode, nkey=0)
        req = JCLBean_pb2.his_minute_req(date=date, code=tag_code_with_nkey)

        # 发送请求并解析返回数据
        req_no = JCLBean_pb2.report_reqno.HIS_MINUTE_REQ  # 请求编号 4068

        his_minute_ans = self._send_and_parse_request(
            req_no,
            req_data=req,
            response_type=JCLBean_pb2.his_minute_ans
        )

        # 如果返回数据为空，则返回 None
        if not his_minute_ans:
            return None

        # 返回历史分时数据列表
        return his_minute_ans

    def get_kline_new(self, code: str, setcode: int = 0, period: int = 0,
                      mulnum: int = 1, offset: int = 0, num: int = 100,
                      tqflag: int = 0, exhq: int = 0) -> Optional[JCLBean_pb2.kline_new_ans]:
        """
        获取新型K线数据（支持日K、周K、月K）

        :param code: 证券代码 (例如 "600000")
        :param setcode: 市场代码 (0-沪 1-深 等，具体参照交易所编码规则)
        :param period: K线周期类型
                      0=日K 1=周K 2=月K 3=年K
                      4=1分钟 5=5分钟 6=15分钟 7=30分钟 8=60分钟
        :param mulnum: 周期乘数 (默认1，如5表示5日/周/月K)
        :param offset: 数据偏移量 (0表示从最新数据开始)
        :param num: 请求数据条数 (默认100条)
        :param tqflag: 复权标志
                       0=不复权 1=前复权 2=后复权 3=定点复权
        :param exhq: 扩展行情标志 (按位使用，0b1:包含成交额 0b10:包含均价)
        :return: kline_new_ans 原型响应对象 或 None
        """
        # 构造代码结构体
        code_info = JCLBean_pb2.tagCodeWithNkey(
            code=code,
            setcode=setcode,
            nkey=0  # 根据协议文档默认填0
        )

        # 构建请求体
        req = JCLBean_pb2.kline_new_req(
            code=code_info,
            period=period,
            mulnum=mulnum,
            offset=offset,
            num=num,
            tqflag=tqflag,
            exhq=exhq
        )

        # 发送并解析请求（直接返回protobuf对象）
        return self._send_and_parse_request(
            req_no=22010,  # NEW_PROTOCOL_KLINE_OFFSET_REQ
            req_data=req,
            response_type=JCLBean_pb2.kline_new_ans
        )

    def get_bkyd_data(self, date: int) -> Optional[List[JCLBean_pb2.bkyd_single_data]]:
        """
        获取板块(大盘)异动数据（开和收）
        :param date: 请求日期（格式示例：20230801）
        :return: 板块异动数据列表
        """
        try:
            # 构造请求数据
            req = JCLBean_pb2.bkyd_date_req(date=date)

            # 发送请求并解析响应
            ans = self._send_and_parse_request(
                req_no=JCLBean_pb2.report_reqno.BKYD_REQ,
                req_data=req,
                response_type=JCLBean_pb2.bkyd_all_ans
            )

            return ans.field if ans else None

        except Exception as e:
            print(e)
            return None

