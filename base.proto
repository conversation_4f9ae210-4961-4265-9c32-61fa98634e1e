syntax = "proto3";

package wuhan.jcl.report;			// 避免消息类型的命名冲突
option go_package = "/quote";


message BaseHead { //消息头
    int32   MsgID            =1;   // 消息业务🆔, 分段控制越小越好，不要超过4位
    uint64  ReqID            =2;   // 请求ID (taskId)
    int32   ProtoType        =3;   // 请求报文类型:0-PB, 1-TCP二进制 
    int32   CompressType     =4;   // 应答报文 是否压缩0-不压缩, 1-采用"LZ4"压缩算法, 2-Snappy压缩
    int32   OriginBodyLength =5;   // 源BaseBody1.MsgData的长度，在RspCompress 压缩算法中会用到
    uint32  CheckSum         =6;   // 校验和，校验和，对于批量请求，第一次请求可以不填写，第二次请求根据第一次请求应答报文头部CheckSum来设置，如果应答结果内容没有不变，则不再重复发送应答。
}

message BaseMsg {
    BaseHead  Head         = 1;   //头
    bytes     MsgData      = 2;   // 消息数据,可以是PB 序列化字节块，也可以是TCP二进制块
}

message RequestAuth {
    string      Token			      = 1; //token由技术中心统一分配，妥善保管,定期更换
    string	    AppName	              = 2;  //app名称,最好英文
    string      AppVer				  = 3;  //app版本
    string      Tag					  = 4;  //标签,可以在应答报文中原样返回
}

message ResponseAuth {
   int32          AuthResult = 1; //应答结果 0 认证成功，非0认证失败
   string         Tag		 = 2; //请求时候带上来的标签
}

enum EnumMsgID {
    Msg_Rsp_DefaultErrorMessage = 0;     //缺省错误应答报文

    Msg_Req_Auth               = 1;      //Token认证请求
    Msg_Req_Heartbeat          = 9;      //心跳包，无Body
    Msg_Req_AutoGbbq           = 22;     //获取指定股票的历史除权除息财务数据，支持传一组股票代码（最多200个）
    Msg_Req_AutoBase           = 23;     //获取指定股票的最新基础财务数据，支持传一组股票代码（最多500个）
    Msg_Req_HostMore           = 31;     //市场配置信息,hostmore

    Msg_Req_HqSub              = 134;    //行情快照订阅，订阅请求会立即应答一次最新行情，应答&推送的数据结构与1728协议应答结构相同，应答协议号为134，推送协议号为1728。订阅请求发二进制协议则推二进制包文，发Proto Buffer请求协议则推Proto Buffer报文。
    Msg_Req_HqUnsub            = 135;    //取消当前链路的订阅，传一组nkey只取消指定的股票，数量传-1则取消该链路上所有订阅，协议请求结构同134协议，无应答数据结构。

    Msg_Req_StrHqSub           = 144;    //H5行情快照订阅，订阅请求会立即应答一次最新行情，应答&推送的数据结构与1728协议应答结构相同，应答协议号为134，推送协议号为1728。订阅请求发二进制协议则推二进制包文，发Proto Buffer请求协议则推Proto Buffer报文。
    Msg_Req_StrHqUnSub         = 145;    //取消当前链路的订阅，传一组nkey只取消指定的股票，数量传-1则取消该链路上所有订阅，协议请求结构同134协议，无应答数据结构。

    Msg_Req_DxjlSub             = 150;   //订阅短线精灵推送请求
    Msg_Req_DxjlUnSub           = 151;   //取消订阅短线精灵推送请求

    Msg_Req_StaticCode=1110;   //请求对应市场代码链
    Msg_Req_StaticCodeEn=1111; //请求对应市场代码链(含中英文名称)

    Msg_Req_UsedName=1120;
    Msg_Req_ZhSort=1205;
    Msg_Req_SortHq=1727;
    Msg_Req_MultiHq=1728;
    Msg_Req_SortNoHqEx=2021;
    Msg_Req_SortHqEx=2023;
    Msg_Req_MultiHqXg=22124;
    Msg_Req_BlockSortHq=2008;
    Msg_Req_SortExHq=2015;
    Msg_Req_DgtlCode=2016;
    Msg_Req_HqEx=2017;
    Msg_Req_StockBlockInfoSet =2019; //个股所属板块pb for app
    Msg_Req_MoneyFlow=1805;
    Msg_Req_MoneyFlowMin1=1807;
    Msg_Req_MoneyRank=2024;
    Msg_Req_AuctionData=10029;
    Msg_Req_AfterTradeKCB=10032;
    Msg_Req_AfterTrade=10036;
    Msg_Req_BlockXmlFile=1360; //请求block.xml zlib 压缩
    Msg_Req_BlockXmlData=13600; //请求板块指数的成分股数据，只支持Proto Buffer协议，请求无参数
    Msg_Req_ZsCfgXmlData=13602;
    Msg_Req_ZdfSection=20001;
    Msg_Req_ZdfResult=20002;
    Msg_Req_ZdfProfit=20003;
    Msg_Req_ZdfZdtNum=20004;
    Msg_Req_FyzbZtResult=20005;
    Msg_Req_HqZdtData=20006;
    Msg_Req_StrategyCenterList=20100;
    Msg_Req_StrategyBsProfit=20101;
    Msg_Req_StrategyBsSuccess=20102;
    Msg_Req_StrategyCenterListJRTC=20111;
    Msg_Req_StrategyCenterListDQCC=20112;
    Msg_Req_StrategyCenterListCLNG=20113;
    Msg_Req_StrategyHisSignal=20114;
    Msg_Req_StrategyOneKey=20104;
    Msg_Req_StrategyOnekeyProfit=20105;
    Msg_Req_StrategyOnekeyResult=20106;
    Msg_Req_BigDataCXQN=20108;
    Msg_ReqBigDataJHJJ=20109;
    Msg_Req_BigDataZTZY=20110;
    Msg_Req_StrategyMMOnekey=20119;
    Msg_Req_StrategyList=20142;
    Msg_Req_StrategyDesc=20145;
    Msg_Req_StrategySystemPool=20160;
    Msg_Req_StrategySystemPoolResult=20161;
    Msg_Req_Dxjl=20400;
    Msg_Req_Bkyd=20405;
    Msg_Req_MinApp=1369;    //app 1-10日分时走势数据
    Msg_Req_KlineApp=1370;  //app k线请求业务
    Msg_Req_KlineNew=22210;
    Msg_Req_KlineTimeNew=22011;
    Msg_Req_MinuteNew=22020;
    Msg_Req_HisMinuteNew=22021;
    Msg_Req_TickApp=4069; //app Tick数据
    Msg_Req_TickNew=22022;
    Msg_Req_TickHisNew=22023;
    Msg_Req_Fjb=22024;
    Msg_Req_StaticsExData=22998;
    Msg_Req_LbData=23041;
    Msg_Req_MoneyFlowStat1=23201;
    Msg_Req_MoneyFlowStat2=23202;
    Msg_Req_MoneyFlowStatBatch=23203;
    Msg_Req_JsonReq=30000;
    Msg_Req_BfdayData=30100;
    Msg_Req_KlineLabel=30101;
    Msg_Req_KlineLabelBatch=30102;
    Msg_Req_L2OrderTick=30300;
    Msg_Req_L2SigOrderTick=30301;
    Msg_Req_L2Mulpk=30302;
    Msg_Req_L2Sigpk=30303;
    Msg_Req_L2KcbPhTick=30304;

    Msg_Req_FdHqStat=20401; //A股分段行情统计,CalcStatics_Agfd_AnsInfo
    
    //zhyt自定义增值服务
    Msg_Req_ValueAddMin=9001; //通用增值分钟类数据协议
    
}
