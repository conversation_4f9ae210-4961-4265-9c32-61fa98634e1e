# **软件开发与交付黄金准则 (Golden Rules for Software Development & Delivery)**

本准则旨在确保所有项目产出均具备卓越的质量、可维护性和可靠性。所有任务必须遵循以下四个核心阶段。

---

#### **第一阶段：规划与设计 (Phase 1: Planning & Design)**

1.  **需求深度剖析 (In-Depth Requirement Analysis)**：在编写任何代码前，必须完全理解业务目标与技术约束。与需求方充分沟通，澄清所有疑点，并定义清晰的“完成标准”（Definition of Done）。
2.  **技术方案制定 (Technical Solution Design)**：将复杂需求分解为模块化、可执行的子任务。设计核心数据结构与算法，明确模块间的接口与依赖关系，并绘制架构草图。
3.  **风险与边界评估 (Risk & Edge Case Assessment)**：主动识别潜在的技术风险、性能瓶颈和安全漏洞。系统性地思考并记录所有边缘情况（如：空值、异常输入、并发冲突等），并将其纳入后续的开发与测试计划。

#### **第二阶段：开发与实现 (Phase 2: Development & Implementation)**

1.  **高质量编码 (High-Quality Coding)**：编写简洁、可读、可维护的代码。遵循统一的编码规范（Style Guide），添加必要的注释以解释复杂逻辑，并确保代码的自解释性。
2.  **健壮的错误处理 (Robust Error Handling)**：代码必须具备优雅处理预期及意外错误的能力。避免靜默失败，通过异常、错误码或日志提供清晰的失败上下文。
3.  **指定模型应用 (Designated Model Application)**：所有代码的生成、分析与优化任务，**必须** 以 `Claude 4 Sonnet` 模型作为核心工具，确保技术栈的一致性与可复现性。

#### **第三阶段：测试与验证 (Phase 3: Testing & Validation)**

1.  **全面测试用例编写 (Comprehensive Test Case Authoring)**：为所有核心功能编写全面的单元测试与集成测试。测试用例必须覆盖：
    * **正常路径** (Happy Path)
    * **边界条件** (Boundary Conditions)
    * **已识别的边缘情况** (Identified Edge Cases)
    * **预期的失败场景** (Expected Failure Scenarios)
2.  **持续验证与迭代 (Continuous Validation & Iteration)**：在开发过程中及完成后，必须运行全量测试。任何测试失败都必须被视为阻塞性缺陷（Blocker），在推进下一步前必须修复。此过程循环直至所有测试稳定通过。

#### **第四阶段：交付与沟通 (Phase 4: Delivery & Communication)**

1.  **标准化交付包 (Standardized Delivery Package)**：每次交付必须是一个完整的、可独立验证的解决方案包，内容包括：
    * **① 工作代码 (Working Code)**：最终的、通过所有测试的源代码。
    * **② 测试套件 (Test Suite)**：用于验证功能的完整测试代码。
    * **③ 清晰文档 (Clear Documentation)**：包含项目简介、环境设置、运行与测试方法的明确说明（例如 `README.md`）。
    * **④ 验证报告 (Validation Report)**：一份简短声明，确认所有测试已成功运行并通过。
2.  **精准高效沟通 (Precise & Efficient Communication)**：所有沟通和文档都应聚焦于核心信息。直接、清晰地呈现解决方案和操作指南，避免无关的细节和冗长的解释，确保信息的可操作性。

---

