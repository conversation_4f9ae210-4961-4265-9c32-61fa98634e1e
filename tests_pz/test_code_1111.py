"""1111 请求市场代码链"""
import sys
from pathlib import Path
# 确保可以从项目根目录导入common包（直接运行该文件时需要）
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))

import pytest
from common.test_base import BaseAPITest
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


class TestCodeInfo(BaseAPITest):
    """测试市场代码链接口一致性"""

    @pytest.mark.parametrize(
        "setcode, startxh",
        load_test_params('test_code_1111_params.yaml'),
        ids=get_param_ids('test_code_1111_params.yaml')
    )
    def test_code_info(self, api_helper, setcode, startxh, logger):
        """请求市场代码链（1111），校验响应时间及内容一致性"""
        # 加载配置
        config = load_test_config('test_code_1111_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        print(f"测试参数: setcode={setcode}, startxh={startxh}")

        # 使用基类方法执行并发测试
        response, new_response, response_times, server_info = self.execute_concurrent_test(
            api_helper, test_config['method_name'], setcode=setcode, startxh=startxh
        )

        # 检查响应有效性并执行断言
        self._validate_and_assert_response(
            response, new_response, test_fields, test_config,
            logger, response_times, server_info
        )

        print("✅ 测试通过")

    def _validate_and_assert_response(self, response, new_response, test_fields, test_config,
                                    logger, response_times, server_info):
        """验证响应并执行断言"""
        response_attr = test_config['response_attribute']

        # 检查响应有效性
        if not hasattr(response, response_attr) or not hasattr(new_response, response_attr):
            self.all_errors.append(f"响应对象缺少{response_attr}属性")
            print(f"⚠️ 响应对象缺少{response_attr}属性")
        elif len(getattr(response, response_attr)) == 0 or len(getattr(new_response, response_attr)) == 0:
            old_len = len(getattr(response, response_attr))
            new_len = len(getattr(new_response, response_attr))
            self.all_errors.append(f"响应数据为空: 旧接口={old_len}, 新接口={new_len}")
            print(f"⚠️ 响应数据为空")
        else:
            old_len = len(getattr(response, response_attr))
            new_len = len(getattr(new_response, response_attr))
            print(f"✅ 数据正常: 旧接口={old_len}条, 新接口={new_len}条")

            # 使用统一断言进行批量比较
            self.assert_and_log(
                response, new_response, test_fields['code_fields'], logger,
                response_times, server_info, test_config['assert_mode'],
                response_attr=response_attr
            )