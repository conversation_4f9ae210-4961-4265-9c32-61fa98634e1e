import sys
from pathlib import Path
# 确保可以从项目根目录导入common包（直接运行该文件时需要）
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))

import pytest
from common.assert_response import assert_response_equal, assert_round_response
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


class TestAnalyTqReq:
    """测试日K、周K、月K数据的接口一致性"""

    @pytest.mark.parametrize(
        "nkey, setcode, code, period, offset, num, mulnum",
        load_test_params('test_analy_tq_1370_params.yaml'),
        ids=get_param_ids('test_analy_tq_1370_params.yaml')
    )
    def test_kline_data(self, api_helper, logger, nkey, setcode, code, period, offset, num, mulnum):
        """测试老接口和新接口的K线数据一致性"""
        # 加载配置
        config = load_test_config('test_analy_tq_1370_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 请求参数
        params = {"nkey": nkey, "setcode": setcode, "code": code, "period": period,
                 "offset": offset, "num": num, "mulnum": mulnum}

        # 并发获取接口响应
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'], **params
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config,
            params, server_info, logger, response_times
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)

    def _run_assertions(self, response, new_response, test_fields, test_config,
                       params, server_info, logger, response_times):
        """执行断言测试"""
        all_errors = []
        print(f"请求参数{params}")

        for response_item, new_response_item in zip(response.aK, new_response.aK):
            # 时间字段测试
            try:
                assert_response_equal(
                    response_item, new_response_item, test_fields['time_fields'],
                    truncate_fields=test_fields['truncate_fields'],
                    request_params=params, server_info=server_info,
                    logger=logger, response_times=response_times
                )
                print("时间字段测试通过")
            except AssertionError as e:
                logger.log_error(e)
                all_errors.append(str(e))

            # 浮点数字段测试
            try:
                assert_round_response(
                    response_item, new_response_item, test_fields['float_fields'],
                    decimal_places=test_config['decimal_places'],
                    request_params=params, server_info=server_info, logger=logger
                )
                print("浮点数字段测试通过")
            except AssertionError as e:
                logger.log_error(e)
                all_errors.append(str(e))

        return all_errors

