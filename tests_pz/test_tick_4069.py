import pytest
import time
from common.assert_response import assert_response_equal, assert_response_equal_with_quotetime, assert_round_response
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


class TestGetTickAns:
    """测试Tick数据接口一致性"""

    @pytest.mark.parametrize(
        "code, setcode, startxh, num",
        load_test_params('test_tick_4069_params.yaml'),
        ids=get_param_ids('test_tick_4069_params.yaml')
    )
    def test_get_tick_ans(self, api_helper, logger, code, setcode, startxh, num):
        """测试tick数据接口（基础参数化版本）"""
        # 加载配置
        config = load_test_config('test_tick_4069_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 创建参数字典用于日志记录
        params = {'code': code, 'setcode': setcode, 'startxh': startxh, 'num': num}

        # 并发调用新旧接口
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'],
            code=code, setcode=setcode, startxh=startxh, num=num
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config, params
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)


    def _run_assertions(self, response, new_response, test_fields, test_config, params):
        """执行断言测试"""
        all_errors = []
        response_attr = test_config['response_attribute']

        # 获取tick数据
        response_ticks = getattr(response, response_attr)
        new_response_ticks = getattr(new_response, response_attr)

        for resp_tick, new_resp_tick in zip(response_ticks, new_response_ticks):
            # quoteTime字段特殊处理
            try:
                assert_response_equal_with_quotetime(
                    resp_tick, new_resp_tick,
                    test_fields['quote_time_fields'],
                    quote_time_length=test_config['quote_time_length']
                )
            except AssertionError as e:
                all_errors.append(str(e))

            # price和vol字段保留小数处理
            try:
                assert_round_response(
                    resp_tick, new_resp_tick,
                    test_fields['price_volume_fields'],
                    decimal_places=test_config['decimal_places']
                )
            except AssertionError as e:
                all_errors.append(str(e))

            # flag字段精确比较
            try:
                assert_response_equal(
                    resp_tick, new_resp_tick,
                    test_fields['flag_fields'],
                    request_params=params
                )
            except AssertionError as e:
                all_errors.append(str(e))

        return all_errors