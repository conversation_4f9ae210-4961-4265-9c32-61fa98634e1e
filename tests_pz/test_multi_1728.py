import sys
from pathlib import Path
# 确保可以从项目根目录导入common包（直接运行该文件时需要）
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))

import pytest
import yaml
import os
from common.test_base import HQTestBase
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


class TestMultiHQ(HQTestBase):
    """多股票行情测试类 - 使用优化后的基类"""

    @pytest.mark.parametrize(
        "nkey, setcode, code",
        load_test_params('test_multi_1728_params.yaml'),
        ids=get_param_ids('test_multi_1728_params.yaml')
    )
    def test_multi_hq(self, api_helper, logger, nkey, setcode, code):
        """测试多股票行情接口，校验响应时间及内容一致性"""
        # 加载配置
        config = load_test_config('test_multi_1728_params.yaml')
        test_config = config['test_config']

        # 加载完整配置以获取市场映射
        yaml_path = os.path.join(os.path.dirname(__file__), '..', 'datas', 'test_multi_1728_params.yaml')
        with open(yaml_path, 'r', encoding='utf-8') as f:
            full_config = yaml.safe_load(f)
        market_mapping = full_config.get('market_mapping', {})

        # 构造单个代码的请求参数
        codes = [{"nkey": nkey, "setcode": setcode, "code": code}]

        # 使用基类方法执行并发测试
        response, new_response, response_times, server_info = self.execute_concurrent_test(
            api_helper, test_config['method_name'], codes=codes
        )

        # 获取行情字段，排除配置中指定的字段
        fields = self.get_hq_fields()
        excluded_fields = config['test_fields']['excluded_fields']
        for field in excluded_fields:
            if field in fields:
                fields.remove(field)

        # 获取市场名称用于日志
        market_name = market_mapping.get(setcode, f"未知市场({setcode})")

        print(f"请求参数: {{'nkey': {nkey}, 'setcode': {setcode}, 'code': '{code}'}} ({market_name})")

        # 使用批量断言进行比较
        self.assert_and_log(
            response, new_response, fields, logger,
            response_times, server_info, test_config['assert_mode'],
            response_attr=test_config['response_attribute']
        )
