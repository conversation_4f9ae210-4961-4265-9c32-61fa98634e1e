import pytest
from common.assert_response import assert_fieldids, assert_round_response
from common.yaml_loader import load_test_params, load_test_config, get_param_ids
# preSettlePrice 不用比


class TestMinuteReq:
    """测试分时数据接口一致性"""

    @pytest.mark.parametrize(
        "setcode, code, days",
        load_test_params('test_minute_req_1369_params.yaml'),
        ids=get_param_ids('test_minute_req_1369_params.yaml')
    )
    def test_minute_req(self, api_helper, setcode, code, days, logger):
        """测试分时数据接口，校验响应时间及内容一致性"""
        # 加载配置
        config = load_test_config('test_minute_req_1369_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 请求参数字典，用于日志记录
        params = {"code": code, "setcode": setcode, "days": days}
        print(f"请求参数: {params}")

        # 并发获取接口响应（使用不同的方法名）
        response, new_response = api_helper.call_method_concurrently(
            None, code=code, setcode=setcode, days=days,
            old_method=test_config['old_method_name'],
            new_method=test_config['new_method_name']
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config,
            params, server_info, logger, response_times
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则统一抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处错误:"] + all_errors)
            raise AssertionError(combined_error)


    def _run_assertions(self, response, new_response, test_fields, test_config,
                       params, server_info, logger, response_times):
        """执行断言测试"""
        all_errors = []

        # 比较行情数据
        all_errors.extend(self._assert_hq_data(
            response, new_response, test_fields, params,
            server_info, logger, response_times
        ))

        # 比较分时数据
        all_errors.extend(self._assert_minute_data(
            response, new_response, test_fields, test_config,
            params, server_info, logger, response_times
        ))

        return all_errors

    def _assert_hq_data(self, response, new_response, test_fields, params,
                       server_info, logger, response_times):
        """断言行情数据"""
        errors = []

        try:
            # 单独检查nkey字段（避免大整数导致内存问题）
            old_nkey = getattr(response.hq, 'nkey', None)
            new_nkey = getattr(new_response.hq, 'nkey', None)
            if old_nkey != new_nkey:
                errors.append(f"nkey字段不匹配: 基准接口={old_nkey}, 新接口={new_nkey}")

            # 比较其他行情字段
            assert_fieldids(
                response.hq, new_response.hq, test_fields['hq_fields'],
                request_params=params, server_info=server_info,
                logger=logger, response_times=response_times
            )
        except Exception as e:
            errors.append(f"行情数据: {str(e)}")

        return errors

    def _assert_minute_data(self, response, new_response, test_fields, test_config,
                           params, server_info, logger, response_times):
        """断言分时数据"""
        errors = []
        max_compare = test_config['max_minute_compare']
        max_errors = test_config['max_errors']

        # 比较分时数据（限制前N条，避免日志过长）
        minute_count = min(max_compare, len(response.minute), len(new_response.minute))

        for i in range(minute_count):
            response_minute = response.minute[i]
            new_response_minute = new_response.minute[i]

            try:
                # 处理avePrice字段的特殊比较
                self._assert_ave_price(
                    response_minute, new_response_minute,
                    test_config['ave_price_tolerance'], errors, i
                )

                # 处理其他字段（排除avePrice，因为已经单独处理）
                other_fields = [f for f in test_fields['minute_fields'] if f != 'avePrice']
                assert_round_response(
                    response_minute, new_response_minute, other_fields,
                    decimal_places=test_config['decimal_places'],
                    request_params=params, server_info=server_info,
                    logger=logger, response_times=response_times
                )
            except Exception as e:
                errors.append(f"第{i+1}条分时数据: {str(e)}")
                # 只记录前N个错误，避免日志过长
                if len(errors) >= max_errors:
                    errors.append(f"... 还有更多错误（已省略）")
                    break

        return errors

    def _assert_ave_price(self, response_minute, new_response_minute, tolerance, errors, index):
        """处理avePrice字段的特殊断言（整数部分比较，允许容差）"""
        if hasattr(response_minute, 'avePrice') and hasattr(new_response_minute, 'avePrice'):
            old_ave_price_int = int(response_minute.avePrice)
            new_ave_price_int = int(new_response_minute.avePrice)
            price_diff = abs(old_ave_price_int - new_ave_price_int)
            # 允许配置的价格差异，这在实时数据中是合理的
            if price_diff > tolerance:
                errors.append(
                    f"第{index+1}条分时数据avePrice字段(整数部分)差异过大: "
                    f"基准接口={old_ave_price_int}, 新接口={new_ave_price_int}, "
                    f"差异={price_diff}, 允许容差={tolerance}"
                )
