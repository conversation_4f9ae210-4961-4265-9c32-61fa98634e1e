"""20401 行情涨幅分段"""
from common.assert_response import assert_response_equal
from common.yaml_loader import load_test_config


class TestHQFDReq:
    """测试行情涨幅分段接口一致性"""

    def test_hqfd_req(self, api_helper, logger):
        """测试行情涨幅分段接口一致性"""
        # 加载配置
        config = load_test_config('test_hqfd_20401_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 创建请求参数字典（此接口无参数）
        params = {}

        logger.log_debug("请求参数：无")
        print("请求参数：无")

        # 并发执行新旧接口
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name']
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config, params
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)

    def _run_assertions(self, response, new_response, test_fields, test_config, params):
        """执行断言测试"""
        all_errors = []

        try:
            assert_response_equal(
                response, new_response,
                fields=test_fields['hqfd_fields'],
                request_params=params
            )
            print("测试通过")
        except Exception as e:
            all_errors.append(str(e))

        return all_errors