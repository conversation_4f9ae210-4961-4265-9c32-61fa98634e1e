import sys
from pathlib import Path
# 确保可以从项目根目录导入common包（直接运行该文件时需要）
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))

import pytest
from common.test_base import KlineTestBase
from common.yaml_loader import load_test_params, load_test_config, get_param_ids

"""
jclTime  日K时间只精确到日 (此注释已了解，对内容比较有指导意义)
"""


class TestKlineData(KlineTestBase):
    """测试K线数据接口一致性"""

    @pytest.mark.parametrize(
        "code, setcode, period, mulnum, offset, num, tqflag, exhq",
        load_test_params('test_kline_22010_params.yaml'),
        ids=get_param_ids('test_kline_22010_params.yaml')
    )
    def test_kline_data(self, api_helper, code, setcode, period, mulnum, offset, num, tqflag, exhq, logger):
        """测试K线数据接口，校验响应时间及内容一致性"""
        # 加载配置
        config = load_test_config('test_kline_22010_params.yaml')
        test_config = config['test_config']

        print(f"测试参数: code={code}, setcode={setcode}, period={period}")

        # 使用基类的通用K线测试方法，配置驱动
        self.run_kline_api_test(
            api_helper, logger,
            old_method=test_config['old_method_name'],
            new_method=test_config['new_method_name'],
            code=code, setcode=setcode, period=period, mulnum=mulnum,
            offset=offset, num=num, tqflag=tqflag, exhq=exhq
        )