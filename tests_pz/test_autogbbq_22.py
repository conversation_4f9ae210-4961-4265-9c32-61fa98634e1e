import pytest
from common.assert_response import assert_round_response
from common.yaml_loader import load_dict_params, load_test_config, get_param_ids


class TestGetAutoGBBQAns:
    """测试股本变迁接口一致性"""

    @pytest.mark.parametrize(
        "code_params",
        load_dict_params('test_autogbbq_22_params.yaml'),
        ids=get_param_ids('test_autogbbq_22_params.yaml')
    )
    def test_get_autogbbq_ans(self, api_helper, code_params, logger):
        """测试新旧股本变迁接口响应一致性"""
        # 加载配置
        config = load_test_config('test_autogbbq_22_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 请求参数
        codes = [code_params]
        print(f"请求参数：{codes}")

        # 并发请求新旧接口
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'], codes
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config,
            codes, server_info, logger, response_times
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=codes, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)

    def _run_assertions(self, response, new_response, test_fields, test_config,
                       codes, server_info, logger, response_times):
        """执行断言测试"""
        all_errors = []

        for response_cw, new_response_cw in zip(response.cqcx[0].cw, new_response.cqcx[0].cw):
            try:
                assert_round_response(
                    response_cw, new_response_cw, test_fields['equity_fields'],
                    decimal_places=test_config['decimal_places'],
                    request_params=codes, server_info=server_info,
                    logger=logger, response_times=response_times
                )
                print("测试通过")
            except Exception as e:
                all_errors.append(str(e))

        return all_errors