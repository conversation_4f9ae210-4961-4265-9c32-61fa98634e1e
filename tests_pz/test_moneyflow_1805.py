"""
1805 单只股票历史资金流接口
"""
#打印差异的跟的日期开发用于排查 (此注释已了解，对内容比较失败时的调试有指导意义)
from common.assert_response import assert_response_equal
from common.yaml_loader import load_test_params, load_test_config, get_param_ids
import pytest
import time


class TestGetMoneyflowAns:
    """测试单只股票历史资金流接口一致性"""

    @pytest.mark.parametrize(
        "code, setcode, offset, num",
        load_test_params('test_moneyflow_1805_params.yaml'),
        ids=get_param_ids('test_moneyflow_1805_params.yaml')
    )
    def test_get_moneyflow_ans(self, api_helper, logger, code, setcode, offset, num):
        """测试单只股票历史资金流接口，校验响应时间及内容一致性"""
        # 加载配置
        config = load_test_config('test_moneyflow_1805_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 创建参数字典用于日志记录
        params = {'code': code, 'setcode': setcode, 'offset': offset, 'num': num}

        # 记录测试开始
        print(f"请求参数: code={code}, setcode={setcode}, offset={offset}, num={num}")

        # 并发执行新旧接口
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'], code=code, setcode=setcode, offset=offset, num=num
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行响应验证和断言测试
        all_errors = self._validate_and_assert_response(
            response, new_response, test_fields, test_config,
            code, params, logger
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)
        else:
            print("✅ 测试通过")


    def _validate_and_assert_response(self, response, new_response, test_fields, test_config,
                                    code, params, logger):
        """验证响应并执行断言测试"""
        # 检查API调用是否成功
        if response is None and new_response is None:
            logger.log_warning(f"新旧接口都返回None，可能股票代码 {code} 无数据或不存在")
            pytest.skip(f"股票代码 {code} 无资金流向数据，跳过测试")
            return []

        if response is None:
            logger.log_error("旧接口返回None")
            pytest.fail("旧接口调用失败，返回None")

        if new_response is None:
            logger.log_error("新接口返回None")
            pytest.fail("新接口调用失败，返回None")

        # 检查响应对象是否有必要的属性
        required_attrs = test_config['required_attributes']
        for attr in required_attrs:
            if not hasattr(response, attr):
                logger.log_error(f"旧接口响应缺少属性: {attr}")
                pytest.fail(f"旧接口响应缺少必要属性: {attr}")
            if not hasattr(new_response, attr):
                logger.log_error(f"新接口响应缺少属性: {attr}")
                pytest.fail(f"新接口响应缺少必要属性: {attr}")

        # 检查数据长度一致性
        self._check_data_length_consistency(response, new_response, logger)

        # 执行数据断言
        return self._assert_data_sets(response, new_response, test_fields, params)

    def _check_data_length_consistency(self, response, new_response, logger):
        """检查数据长度一致性"""
        if len(response.amt) != len(new_response.amt):
            logger.log_warning(f"amt数据长度不一致: 旧接口={len(response.amt)}, 新接口={len(new_response.amt)}")

        if len(response.vol) != len(new_response.vol):
            logger.log_warning(f"vol数据长度不一致: 旧接口={len(response.vol)}, 新接口={len(new_response.vol)}")

        if len(response.cnt) != len(new_response.cnt):
            logger.log_warning(f"cnt数据长度不一致: 旧接口={len(response.cnt)}, 新接口={len(new_response.cnt)}")

    def _assert_data_sets(self, response, new_response, test_fields, params):
        """断言三个数据集的内容"""
        all_errors = []
        fields = test_fields['moneyflow_fields']

        # 比较amt数据
        for i, (response_amt, new_response_amt) in enumerate(zip(response.amt, new_response.amt)):
            try:
                assert_response_equal(response_amt, new_response_amt, fields=fields, request_params=params)
            except AssertionError as e:
                error_msg = f"amt数据第{i+1}条不一致: {str(e)}"
                all_errors.append(error_msg)

        # 比较vol数据
        for i, (response_vol, new_response_vol) in enumerate(zip(response.vol, new_response.vol)):
            try:
                assert_response_equal(response_vol, new_response_vol, fields=fields, request_params=params)
            except AssertionError as e:
                error_msg = f"vol数据第{i+1}条不一致: {str(e)}"
                all_errors.append(error_msg)

        # 比较cnt数据
        for i, (response_cnt, new_response_cnt) in enumerate(zip(response.cnt, new_response.cnt)):
            try:
                assert_response_equal(response_cnt, new_response_cnt, fields=fields, request_params=params)
            except AssertionError as e:
                error_msg = f"cnt数据第{i+1}条不一致: {str(e)}"
                all_errors.append(error_msg)

        return all_errors
