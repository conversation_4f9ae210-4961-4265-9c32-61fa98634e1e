import pytest
from common.assert_response import assert_financial_data_equal
from common.yaml_loader import load_nested_params, load_test_config, get_param_ids


class TestGetAutobaseAns:
    """测试财务数据接口一致性"""

    @pytest.mark.parametrize(
        "codes",
        load_nested_params('test_get_autobase_23_params.yaml'),
        ids=get_param_ids('test_get_autobase_23_params.yaml')
    )
    def test_get_autobase_ans(self, api_helper, logger, codes):
        """测试财务数据接口一致性"""
        # 加载配置
        config = load_test_config('test_get_autobase_23_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        logger.log_debug(f"请求参数{codes}")
        print(f"请求参数{codes}")

        # 并发执行新旧接口（使用不同的方法名）
        response, new_response = api_helper.call_method_concurrently(
            None, codes,
            old_method=test_config['old_method_name'],
            new_method=test_config['new_method_name']
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config,
            codes, logger
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=codes, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)

    def _run_assertions(self, response, new_response, test_fields, test_config, codes, logger):
        """执行断言测试"""
        all_errors = []

        for resp_item, new_resp_item in zip(response, new_response):
            try:
                print(f"老接口响应：{resp_item}-新接口响应：{new_resp_item}-比对字段{test_fields['financial_fields']}")
                # 使用专门的财务数据比较函数，ActiveCapital和zgb字段转换为整数
                assert_financial_data_equal(
                    resp_item, new_resp_item, test_fields['financial_fields'],
                    request_params=codes
                )
            except Exception as e:
                all_errors.append(str(e))

        return all_errors