"""
1110：请求对应市场代码链(含中英文名称)
"""
import pytest
from common.assert_response import assert_response_equal, assert_round_response
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


class TestMarketCode:
    """测试市场代码链接口一致性"""

    @pytest.mark.parametrize(
        "setcode, startxh",
        load_test_params('test_market_1110_params.yaml'),
        ids=get_param_ids('test_market_1110_params.yaml')
    )
    def test_market_code(self, api_helper, logger, setcode, startxh):
        """测试市场代码接口(1110)，并校验响应时间和内容一致性"""
        # 加载配置
        config = load_test_config('test_market_1110_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 创建参数字典用于日志记录
        params = {'setcode': setcode, 'startxh': startxh}

        print(f"请求参数：setcode={setcode},startxh={startxh}")

        # 并发调用新旧接口
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'],
            setcode=setcode, startxh=startxh
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config, params
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)


    def _run_assertions(self, response, new_response, test_fields, test_config, params):
        """执行断言测试"""
        all_errors = []
        response_attr = test_config['response_attribute']
        max_items = test_config['max_compare_items']

        response_codes = getattr(response, response_attr)[:max_items]
        new_response_codes = getattr(new_response, response_attr)[:max_items]

        for i, (response_code, new_response_code) in enumerate(zip(response_codes, new_response_codes)):
            # 比较普通字段
            try:
                assert_response_equal(
                    response_code, new_response_code,
                    test_fields['normal_fields'], request_params=params
                )
            except AssertionError as e:
                error_msg = f"第{i+1}条代码普通字段断言失败: {str(e)}"
                all_errors.append(error_msg)

            # 比较Close字段，保留2位小数
            try:
                close_precision = test_config['decimal_places']
                assert round(response_code.Close, close_precision) == round(new_response_code.Close, close_precision)
            except AssertionError as e:
                error_msg = (f"第{i+1}条代码Close字段断言失败: 新接口={new_response_code.Close}, "
                           f"老接口={response_code.Close}, 四舍五入后: 新接口={round(new_response_code.Close, close_precision)}, "
                           f"老接口={round(response_code.Close, close_precision)}")
                all_errors.append(error_msg)

            # 比较VolBase字段，保留2位小数
            try:
                assert_round_response(
                    response_code, new_response_code,
                    test_fields['decimal_fields'],
                    decimal_places=test_config['decimal_places'],
                    request_params=params
                )
                print("测试通过")
            except AssertionError as e:
                error_msg = f"第{i+1}条代码VolBase字段断言失败: {str(e)}"
                all_errors.append(error_msg)

        return all_errors