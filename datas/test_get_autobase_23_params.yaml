# 财务数据测试参数配置
# 格式: codes (股票代码列表)

kline_test_params:
  # 单个股票测试场景
  single_stock_tests:
    - name: "SH-600000-single"
      params: [[{"code": "600000", "setcode": 1, "nkey": 0}]]
      description: "单个沪市股票"
    - name: "SZ-000001-single"
      params: [[{"code": "000001", "setcode": 0, "nkey": 0}]]
      description: "单个深市股票"
    - name: "SZ-300750-single"
      params: [[{"code": "300750", "setcode": 0, "nkey": 0}]]
      description: "单个创业板股票"
    - name: "SH-688001-single"
      params: [[{"code": "688001", "setcode": 1, "nkey": 0}]]
      description: "单个科创板股票"
    - name: "BJ-830001-single"
      params: [[{"code": "830001", "setcode": 2, "nkey": 0}]]
      description: "单个北交所股票"

  # 多个股票测试场景
  multiple_stocks_tests:
    - name: "SH-multiple-same-market"
      params: [[{"code": "600000", "setcode": 1, "nkey": 0}, {"code": "600519", "setcode": 1, "nkey": 0}]]
      description: "多个沪市主板股票(同市场)"
    - name: "SZ-multiple-mixed-boards"
      params: [[{"code": "000001", "setcode": 0, "nkey": 0}, {"code": "000002", "setcode": 0, "nkey": 0}, {"code": "300001", "setcode": 0, "nkey": 0}]]
      description: "多个深市股票(同市场含主板创业板)"
    - name: "SH-SZ-main-boards"
      params: [[{"code": "600000", "setcode": 1, "nkey": 0}, {"code": "000001", "setcode": 0, "nkey": 0}]]
      description: "多个混合市场股票(沪深主板)"
    - name: "STAR-GEM-boards"
      params: [[{"code": "688001", "setcode": 1, "nkey": 0}, {"code": "300750", "setcode": 0, "nkey": 0}]]
      description: "多个混合市场股票(科创板创业板)"
    - name: "mixed-with-bj"
      params: [[{"code": "000001", "setcode": 0, "nkey": 0}, {"code": "600000", "setcode": 1, "nkey": 0}]]
      description: "多个混合市场股票(含北交所)"

# 测试字段配置
test_fields:
  financial_fields: 
    - 'ActiveCapital'
    - 'gxrq'
    - 'zgb'
    - 'gjg'
    - 'zzc'
    - 'gdzc'
    - 'wxzc'
    - 'cqtz'
    - 'cqfz'
    - 'zbgjj'
    - 'jzc'
    - 'zysy'
    - 'yyly'
    - 'tzsy'
    - 'yywsz'
    - 'lyze'
    - 'shly'
    - 'jly'
    - 'wfply'
    - 'tzmgjz'
    - 'HalfYearFlag'
    - 'start'

# 测试配置
test_config:
  old_method_name: 'get_market_config'
  new_method_name: 'get_financial_data'
  assert_function: 'assert_financial_data_equal'
  
# 字段处理配置
field_processing:
  # ActiveCapital: 10以内误差
  # zgb: 10以内误差
  tolerance_fields:
    - 'ActiveCapital'
    - 'zgb'
  tolerance_value: 10
