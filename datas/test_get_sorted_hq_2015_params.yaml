# 分类排序行情接口测试参数配置 (2015)
# 格式: [setDomain, coltype, startxh, wantnum, sorttype, drate, fieldids]

kline_test_params:
  # 原始测试场景
  original_tests:
    - name: "original-涨停价升序"
      params: [0, 17, 0, 10, 1, 0, [1, 2, 3, 17]]
      description: "原始测试-按涨停价升序"

  # 沪市A股测试
  shanghai_a_tests:
    - name: "SH-A-最新价降序"
      params: [1, 13, 0, 20, 2, 0, [13, 9, 10, 11, 12, 24, 26]]
      description: "沪市A股-按最新价降序"

  # 全市场测试
  all_market_tests:
    - name: "ALL-成交量降序-分页"
      params: [0, 24, 10, 10, 2, 0, [13, 24, 26, 14]]
      description: "全市场-按成交量降序-分页第二页"
    - name: "ALL-涨速降序-drate1"
      params: [0, 14, 0, 10, 2, 1, [13, 14, 9, 24]]
      description: "全市场-按涨速降序-drate为1"
    - name: "ALL-内盘量升序"
      params: [0, 28, 0, 10, 1, 0, [13, 28, 29, 24]]
      description: "全市场-按内盘量升序"

  # 创业板测试
  gem_tests:
    - name: "GEM-市盈率升序"
      params: [10, 23, 0, 15, 1, 0, [13, 23, 24, 26]]
      description: "创业板-按市盈率升序"

  # 科创板测试
  star_tests:
    - name: "STAR-成交额降序"
      params: [11, 26, 0, 5, 2, 0, [13, 26, 24, 14]]
      description: "科创板-按成交额降序-取5条"

  # 深市A股测试
  shenzhen_a_tests:
    - name: "SZ-A-最高价降序"
      params: [2, 11, 0, 10, 2, 0, [13, 11, 9]]
      description: "深市A股-按最高价降序-少量字段"

# 测试字段配置
test_fields:
  hq_fields:
    - 'nkey'
    - 'Status'
    - 'InOutFlag'
    - 'TickCount'
    - 'PreClosePrice'
    - 'OpenPrice'
    - 'HighPrice'
    - 'LowPrice'
    - 'NowPrice'
    - 'zangsu'
    - 'AveragePrice'
    - 'LimitUpPrice'
    - 'LimitDownPrice'
    - 'Volume'
    - 'NowVol'
    - 'Amount'
    - 'NowAmount'
    - 'Inside'
    - 'Outside'
    - 'AllBuyPriceCount'
    - 'AllSellPriceCount'
    - 'BuyPrice'
    - 'BuyVolume'
    - 'SellPrice'
    - 'SellVolume'

# 测试配置
test_config:
  method_name: 'get_sort_ex_hq'
  response_structure: 'fields'
  assert_function: 'assert_response_equal'
  
# 字段处理配置
field_processing:
  comparison_mode: 'exact'  # 精确比较模式
  
# 接口说明
interface_info:
  description: "2015 分类排序行情接口"
  msg_id: 2015
  parameters: "setDomain, coltype, startxh, wantnum, sorttype, drate, fieldids"
  response_structure: "fields[].hq"
  notes: "暂不测试，接口还未改好"
