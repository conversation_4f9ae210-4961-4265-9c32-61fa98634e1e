# 排序行情扩展接口测试参数配置 (2021)
# 格式: [setDomain, coltype, startxh, wantnum, sorttype, drate, fieldids]

kline_test_params:
  # 基础测试用例（原参数）
  basic_tests:
    - name: "深市-按涨停价升序-基础字段"
      params: [0, 14, 0, 10, 1, 0, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]]
      description: "深市-按涨停价升序-基础字段测试"

  # 沪市A股测试
  shanghai_a_tests:
    - name: "沪市A股-按最新价降序"
      params: [1, 13, 0, 20, 2, 0, [13, 9, 10, 11, 12, 24, 26]]
      description: "沪市A股-按最新价降序-20条记录"

  # 全市场测试
  all_market_tests:
    - name: "全市场-按成交量降序-分页第二页"
      params: [0, 24, 10, 10, 2, 0, [13, 24, 26, 14]]
      description: "全市场-按成交量降序-分页第二页-10条记录"

  # 创业板测试
  gem_tests:
    - name: "创业板-按市盈率升序"
      params: [10, 23, 0, 15, 1, 0, [13, 23, 24, 26]]
      description: "创业板-按市盈率升序-15条记录"

  # 科创板测试
  star_tests:
    - name: "科创板-按成交额降序-取5条"
      params: [11, 26, 0, 5, 2, 0, [13, 26, 24, 14]]
      description: "科创板-按成交额降序-取5条记录"

# 测试字段配置
test_fields:
  hq_fields:
    - 'Status'
    - 'InOutFlag'
    - 'TickCount'
    - 'PreClosePrice'
    - 'OpenPrice'
    - 'HighPrice'
    - 'LowPrice'
    - 'NowPrice'
    - 'zangsu'
    - 'AveragePrice'
    - 'LimitUpPrice'
    - 'LimitDownPrice'
    - 'Volume'
    - 'NowVol'
    - 'Amount'
    - 'NowAmount'
    - 'Inside'
    - 'Outside'
    - 'AllBuyPriceCount'
    - 'AllSellPriceCount'
    - 'BuyPrice'
    - 'BuyVolume'
    - 'SellPrice'
    - 'SellVolume'
  
  excluded_fields:
    - 'nkey'  # 排除nkey避免内存问题

# 测试配置
test_config:
  method_name: 'get_sort_ex_hq'
  response_structure: 'fields'
  assert_function: 'assert_fieldids'
  
# 字段处理配置
field_processing:
  # nkey字段特殊处理：单独检查避免大整数导致的内存问题
  special_nkey_handling: true
  comparison_mode: 'fieldids'  # 使用fieldids专用断言
  
# 接口说明
interface_info:
  description: "2021 排序行情扩展接口"
  msg_id: 2021
  parameters: "setDomain, coltype, startxh, wantnum, sorttype, drate, fieldids"
  response_structure: "fields[].hq"
  notes: "nkey字段单独处理避免内存问题，其他字段使用assert_fieldids进行比较"
