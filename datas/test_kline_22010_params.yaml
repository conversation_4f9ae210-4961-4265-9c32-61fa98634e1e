# K线数据测试参数配置 (22010)
# 格式: [code, setcode, period, mulnum, offset, num, tqflag, exhq]

kline_test_params:
  # 基础K线测试场景
  basic_kline_tests:
    - name: "SZ-000001-daily"
      params: ["000001", 0, 1, 1, 0, 10, 0, 0]
      description: "深市日K线"
    - name: "SH-600000-daily"
      params: ["600000", 1, 1, 1, 0, 10, 0, 0]
      description: "沪市日K线"
    - name: "SZ-000001-minute"
      params: ["000001", 0, 4, 1, 0, 5, 0, 0]
      description: "深市分时数据"
    - name: "SH-600000-weekly-adj"
      params: ["600000", 1, 2, 1, 0, 20, 1, 0]
      description: "沪市周K线-前复权"
    - name: "GEM-300115-extended"
      params: ["300115", 0, 7, 1, 0, 5, 0, 1]
      description: "创业板-包含扩展行情"

  # 特殊测试场景
  special_tests:
    - name: "GEM-300115-minute"
      params: ["300115", 0, 0, 1, 0, 5, 0, 0]
      description: "创业板分时数据"
    - name: "INDEX-399001-5min"
      params: ["399001", 0, 5, 1, 0, 12, 0, 0]
      description: "深证成指5分钟K线"

# 测试字段配置
test_fields:
  kline_fields:
    - 'jclTime'
    - 'openPrice'
    - 'highPrice'
    - 'lowPrice'
    - 'closePrice'
    - 'volume'
    - 'amount'
    - 'avePrice'

# 测试配置
test_config:
  old_method_name: 'get_kline_new'
  new_method_name: 'get_kline_data'
  response_attribute: 'klines'
  assert_function: 'assert_kline_response'
  
# 字段处理配置
field_processing:
  # jclTime 日K时间只精确到日
  time_precision: 'day'  # 日K时间精度
  decimal_places: 2  # 价格字段保留小数位数
  
# 接口说明
interface_info:
  description: "22010 K线数据接口"
  msg_id: 22010
  parameters: "code, setcode, period, mulnum, offset, num, tqflag, exhq"
  notes: "jclTime 日K时间只精确到日"
