# 主机配置测试参数配置
# 格式: [setcode, verflag]

kline_test_params:
  # 最新版本测试场景 (verflag=2)
  latest_version_tests:
    - name: "SZ-latest-v2"
      params: [0, 2]
      description: "深市-最新版本"
    - name: "SH-latest-v2"
      params: [1, 2]
      description: "沪市-最新版本"
    - name: "BJ-latest-v2"
      params: [2, 2]
      description: "北交所-最新版本"

  # 默认/稳定版本测试场景 (verflag=0)
  default_version_tests:
    - name: "SZ-default-v0"
      params: [0, 0]
      description: "深市-默认版本"
    - name: "SH-default-v0"
      params: [1, 0]
      description: "沪市-默认版本"
    - name: "BJ-default-v0"
      params: [2, 0]
      description: "北交所-默认版本"

  # 特定版本测试场景 (verflag=1)
  specific_version_tests:
    - name: "SZ-specific-v1"
      params: [0, 1]
      description: "深市-特定版本1"
    - name: "SH-specific-v1"
      params: [1, 1]
      description: "沪市-特定版本1"

# 测试字段配置
test_fields:
  host_fields: 
    - 'common'
    - 'qt'
    - 'infcodedate'
    - 'infcodehms'
    - 'bserv1'
    - 'bserv3'
    - 'nUrgentNum'
    - 'linuxcheck'
    - 'b5mmp'

# 测试配置
test_config:
  old_method_name: 'get_hostmore_req'
  new_method_name: 'get_market_config'
  response_attribute: 'hosts'
  assert_function: 'assert_response_equal'
  
# 字段处理配置
field_processing:
  # 主机配置字段通常为字符串或整数，直接比较即可
  comparison_mode: 'exact'  # 精确比较模式
