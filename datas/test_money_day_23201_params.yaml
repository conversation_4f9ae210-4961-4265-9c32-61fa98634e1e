# 资金流向日周期测试参数配置 (23201)
# 格式: [code, setcode, offset, num]

kline_test_params:
  # 深市主板股票测试
  shenzhen_main_board:
    - name: "SZ-000001-latest5"
      params: ["000001", 0, 0, 5]
      description: "深市主板-000001-最新5日"
    - name: "SZ-000001-offset3"
      params: ["000001", 0, 3, 5]
      description: "深市主板-000001-偏移3取5"
    - name: "SZ-000002-yesterday"
      params: ["000002", 0, 1, 1]
      description: "深市主板-000002-偏移1取1(昨日)"
    - name: "SZ-002415-latest10"
      params: ["002415", 0, 0, 10]
      description: "深市主板-002415-最新10日"

  # 沪市主板股票测试
  shanghai_main_board:
    - name: "SH-600000-latest10"
      params: ["600000", 1, 0, 10]
      description: "沪市主板-600000-最新10日"
    - name: "SH-600000-offset10"
      params: ["600000", 1, 10, 20]
      description: "沪市主板-600000-偏移10取20"
    - name: "SH-601318-latest15"
      params: ["601318", 1, 0, 15]
      description: "沪市主板-601318-最新15日"
    - name: "SH-600036-offset5"
      params: ["600036", 1, 5, 10]
      description: "沪市主板-600036-偏移5取10"

  # 创业板股票测试
  gem_board:
    - name: "GEM-300059-latest10"
      params: ["300059", 0, 0, 10]
      description: "创业板-300059-最新10日"
    - name: "GEM-300059-offset2"
      params: ["300059", 0, 2, 15]
      description: "创业板-300059-偏移2取15"
    - name: "GEM-300750-latest5"
      params: ["300750", 0, 0, 5]
      description: "创业板-300750-最新5日"
    - name: "GEM-300015-offset1"
      params: ["300015", 0, 1, 8]
      description: "创业板-300015-偏移1取8"

# 测试字段配置
test_fields:
  money_flow_fields:
    - 'jcltime'
    - 'superIn'
    - 'superOut'
    - 'bigIn'
    - 'bigOut'
    - 'midIn'
    - 'midOut'
    - 'smallIn'
    - 'smallOut'

# 测试配置
test_config:
  method_name: 'get_money_flow_day'
  decimal_places: 2
  required_attributes: ['amt', 'vol', 'cnt']
  data_types: ['amt', 'vol', 'cnt']
  
# 字段处理配置
field_processing:
  decimal_places: 2  # 资金流向字段保留2位小数
  comparison_mode: 'round'  # 使用四舍五入比较模式
  
# 接口说明
interface_info:
  description: "23201 资金流向日周期接口"
  msg_id: 23201
  parameters: "code, setcode, offset, num"
  response_structure: "amt, vol, cnt 三个数据集"
  notes: "每个数据集包含资金流向的9个字段，需要保留2位小数进行比较"
