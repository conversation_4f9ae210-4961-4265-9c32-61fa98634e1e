# 市场代码链测试参数配置
# 格式: [setcode, startxh]

kline_test_params:
  # 主要测试用例（确保有数据的市场）
  main_tests:
    - name: "SZ-market-start0"
      params: [0, 0]
      description: "深市，从头开始获取代码链"
    - name: "SH-market-start0"
      params: [1, 0]
      description: "沪市，从头开始获取代码链"

  # 分页测试（使用较小的起始序号）
  pagination_tests:
    - name: "SZ-market-start1"
      params: [0, 1]
      description: "深市，从第2条记录开始获取"
    - name: "SH-market-start1"
      params: [1, 1]
      description: "沪市，从第2条记录开始获取"
    - name: "SZ-market-start10"
      params: [0, 10]
      description: "深市，从第11条记录开始获取"
    - name: "SH-market-start10"
      params: [1, 10]
      description: "沪市，从第11条记录开始获取"

# 测试字段配置
test_fields:
  code_fields: ['nkey', 'Code', 'Unit', 'VolBase', 'precise', 'Close', 'nFZ', 'BaseFreshCount']

# 测试配置
test_config:
  method_name: 'get_code_info'
  response_attribute: 'code'
  assert_mode: 'batch'
  
# 字段处理配置
field_processing:
  response_attr: 'code'  # 指定响应属性
