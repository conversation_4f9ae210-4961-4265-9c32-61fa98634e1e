# 行情涨幅分段测试参数配置
# 这是一个无参数接口，但可以配置测试行为

kline_test_params:
  # 基础测试场景（无参数接口）
  basic_tests:
    - name: "hqfd-basic-test"
      params: []
      description: "行情涨幅分段基础测试"

# 测试字段配置
test_fields:
  hqfd_fields: 
    - 'field'
    - 'fieldsh'
    - 'fieldsz'
    - 'fieldcy'

# 测试配置
test_config:
  method_name: 'get_hqfd_req'
  has_parameters: false
  assert_function: 'assert_response_equal'
  
# 字段处理配置
field_processing:
  # 行情涨幅分段字段通常为数组或列表，直接比较
  comparison_mode: 'exact'  # 精确比较模式
  ignore_indices: [6]  # 第6个索引值不对比（根据注释）
  
# 接口说明
interface_info:
  description: "20401 行情涨幅分段接口"
  msg_id: 20401
  parameters: "无参数"
  response_fields: "field, fieldsh, fieldsz, fieldcy"
  notes: "field 第6个索引值不对比"
