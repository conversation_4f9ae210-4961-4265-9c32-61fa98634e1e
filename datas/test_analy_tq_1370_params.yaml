# K线数据测试参数配置
# 格式: [nkey, setcode, code, period, offset, num, mulnum]

kline_test_params:
  # 沪市股票 (setcode=1, code="600000")
  shanghai_stock:
    - name: "SH-5min-K-offset0"
      params: [0, 0, "301156", 0, 0, 48, 1]
    - name: "SH-5min-K-offset2"
      params: [0, 1, "600000", 0, 2, 48, 1]
    - name: "SH-5min-K-offset3"
      params: [0, 1, "600000", 0, 3, 48, 1]
    - name: "SH-15min-K-offset1"
      params: [0, 1, "600000", 1, 1, 16, 1]
    - name: "SH-15min-K-offset2"
      params: [0, 1, "600000", 1, 2, 16, 1]
    - name: "SH-15min-K-offset3"
      params: [0, 1, "600000", 1, 3, 16, 1]
    - name: "SH-30min-K-offset1"
      params: [0, 1, "600000", 2, 1, 8, 1]
    - name: "SH-30min-K-offset2"
      params: [0, 1, "600000", 2, 2, 8, 1]
    - name: "SH-30min-K-offset3"
      params: [0, 1, "600000", 2, 3, 8, 1]
    - name: "SH-60min-K-offset1"
      params: [0, 1, "600000", 3, 1, 4, 1]
    - name: "SH-60min-K-offset2"
      params: [0, 1, "600000", 3, 2, 4, 1]
    - name: "SH-60min-K-offset3"
      params: [0, 1, "600000", 3, 3, 4, 1]
    - name: "SH-daily-K-offset1"
      params: [0, 1, "600000", 4, 1, 2, 1]
    - name: "SH-daily-K-offset2"
      params: [0, 1, "600000", 4, 2, 2, 1]
    - name: "SH-daily-K-offset3"
      params: [0, 1, "600000", 4, 3, 2, 1]
    - name: "SH-weekly-K-offset1"
      params: [0, 1, "600000", 5, 1, 2, 1]
    - name: "SH-weekly-K-offset2"
      params: [0, 1, "600000", 5, 2, 2, 1]
    - name: "SH-weekly-K-offset3"
      params: [0, 1, "600000", 5, 3, 2, 1]
    - name: "SH-monthly-K-offset1"
      params: [0, 1, "600000", 6, 1, 2, 1]
    - name: "SH-monthly-K-offset2"
      params: [0, 1, "600000", 6, 2, 2, 1]
    - name: "SH-monthly-K-offset3"
      params: [0, 1, "600000", 6, 3, 2, 1]
    - name: "SH-1min-K-offset1"
      params: [0, 1, "600000", 7, 1, 240, 1]
    - name: "SH-1min-K-offset2"
      params: [0, 1, "600000", 7, 2, 240, 1]
    - name: "SH-1min-K-offset3"
      params: [0, 1, "600000", 7, 3, 240, 1]
    - name: "SH-quarterly-K-offset1"
      params: [0, 1, "600000", 10, 1, 2, 1]
    - name: "SH-quarterly-K-offset2"
      params: [0, 1, "600000", 10, 2, 2, 1]
    - name: "SH-quarterly-K-offset3"
      params: [0, 1, "600000", 10, 3, 2, 1]
    - name: "SH-yearly-K-offset1"
      params: [0, 1, "600000", 11, 1, 2, 1]
    - name: "SH-yearly-K-offset2"
      params: [0, 1, "600000", 11, 2, 2, 1]
    - name: "SH-yearly-K-offset3"
      params: [0, 1, "600000", 11, 3, 2, 1]

  # 深市股票 (setcode=0, code="000001")
  shenzhen_stock:
    - name: "SZ-5min-K-offset1"
      params: [0, 0, "000001", 0, 1, 48, 1]
    - name: "SZ-5min-K-offset2"
      params: [0, 0, "000001", 0, 2, 48, 1]
    - name: "SZ-5min-K-offset3"
      params: [0, 0, "000001", 0, 3, 48, 1]
    - name: "SZ-15min-K-offset1"
      params: [0, 0, "000001", 1, 1, 16, 1]
    - name: "SZ-15min-K-offset2"
      params: [0, 0, "000001", 1, 2, 16, 1]
    - name: "SZ-15min-K-offset3"
      params: [0, 0, "000001", 1, 3, 16, 1]
    - name: "SZ-30min-K-offset1"
      params: [0, 0, "000001", 2, 1, 8, 1]
    - name: "SZ-30min-K-offset2"
      params: [0, 0, "000001", 2, 2, 8, 1]
    - name: "SZ-30min-K-offset3"
      params: [0, 0, "000001", 2, 3, 8, 1]
    - name: "SZ-60min-K-offset1"
      params: [0, 0, "000001", 3, 1, 4, 1]
    - name: "SZ-60min-K-offset2"
      params: [0, 0, "000001", 3, 2, 4, 1]
    - name: "SZ-60min-K-offset3"
      params: [0, 0, "000001", 3, 3, 4, 1]
    - name: "SZ-daily-K-offset1"
      params: [0, 0, "000001", 4, 1, 2, 1]
    - name: "SZ-daily-K-offset2"
      params: [0, 0, "000001", 4, 2, 2, 1]
    - name: "SZ-daily-K-offset3"
      params: [0, 0, "000001", 4, 3, 2, 1]
    - name: "SZ-weekly-K-offset1"
      params: [0, 0, "000001", 5, 1, 2, 1]
    - name: "SZ-weekly-K-offset2"
      params: [0, 0, "000001", 5, 2, 2, 1]
    - name: "SZ-weekly-K-offset3"
      params: [0, 0, "000001", 5, 3, 2, 1]
    - name: "SZ-monthly-K-offset1"
      params: [0, 0, "000001", 6, 1, 2, 1]
    - name: "SZ-monthly-K-offset2"
      params: [0, 0, "000001", 6, 2, 2, 1]
    - name: "SZ-monthly-K-offset3"
      params: [0, 0, "000001", 6, 3, 2, 1]
    - name: "SZ-1min-K-offset1"
      params: [0, 0, "000001", 7, 1, 240, 1]
    - name: "SZ-1min-K-offset2"
      params: [0, 0, "000001", 7, 2, 240, 1]
    - name: "SZ-1min-K-offset3"
      params: [0, 0, "000001", 7, 3, 240, 1]
    - name: "SZ-quarterly-K-offset1"
      params: [0, 0, "000001", 10, 1, 2, 1]
    - name: "SZ-quarterly-K-offset2"
      params: [0, 0, "000001", 10, 2, 2, 1]
    - name: "SZ-quarterly-K-offset3"
      params: [0, 0, "000001", 10, 3, 2, 1]
    - name: "SZ-yearly-K-offset1"
      params: [0, 0, "000001", 11, 1, 2, 1]
    - name: "SZ-yearly-K-offset2"
      params: [0, 0, "000001", 11, 2, 2, 1]
    - name: "SZ-yearly-K-offset3"
      params: [0, 0, "000001", 11, 3, 2, 1]

# 测试字段配置
test_fields:
  time_fields: ['jclTime']
  float_fields: ['fOpen', 'fHigh', 'fLow', 'fClose', 'YClose', 'fAmount', 'dVolume']
  truncate_fields:
    jclTime: 10
    QuoteTime: 10
    quoteTime: 10

# 测试配置
test_config:
  decimal_places: 2
  method_name: 'get_adjusted_kline'
