# 市场代码链测试参数配置 (1110)
# 格式: [setcode, startxh]

kline_test_params:
  # 基础测试场景（从头开始）
  basic_tests:
    - name: "SZ-start0"
      params: [0, 0]
      description: "默认(深市从头)"
    - name: "SH-start0"
      params: [1, 0]
      description: "沪市从头"
    - name: "BJ-start0"
      params: [2, 0]
      description: "北交所从头"

  # 分页测试场景（大跳跃）
  pagination_large_tests:
    - name: "SZ-page100"
      params: [0, 100]
      description: "深市分页跳过100"
    - name: "SH-page200"
      params: [1, 200]
      description: "沪市分页跳过200"
    - name: "BJ-page50"
      params: [2, 50]
      description: "北交所分页跳过50"

  # 分页测试场景（小跳跃）
  pagination_small_tests:
    - name: "SZ-page1"
      params: [0, 1]
      description: "深市分页跳过1"
    - name: "SH-page10"
      params: [1, 10]
      description: "沪市分页跳过10"

# 测试字段配置
test_fields:
  normal_fields: ["Code", "Unit", "precise", "nFZ", "BaseFreshCount"]
  decimal_fields: ["VolBase"]
  special_fields: ["Close"]

# 测试配置
test_config:
  method_name: 'get_code_list'
  response_attribute: 'code'
  max_compare_items: 10  # 最多比较前10条记录
  decimal_places: 2  # 小数位数
  
# 字段处理配置
field_processing:
  close_field_precision: 2  # Close字段保留2位小数
  volbase_field_precision: 2  # VolBase字段保留2位小数
  comparison_mode: 'mixed'  # 混合比较模式（普通字段+特殊字段）
  
# 接口说明
interface_info:
  description: "1110 请求对应市场代码链(含中英文名称)"
  msg_id: 1110
  parameters: "setcode, startxh"
  response_fields: "Code, Unit, precise, nFZ, BaseFreshCount, VolBase, Close"
  notes: "Close和VolBase字段需要保留2位小数进行比较"
