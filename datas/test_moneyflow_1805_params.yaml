# 单只股票历史资金流测试参数配置 (1805)
# 格式: [code, setcode, offset, num]

kline_test_params:
  # 主板股票（上海）测试
  shanghai_main_board:
    - name: "SH-600000-latest10"
      params: ["600000", 1, 0, 10]
      description: "浦发银行-最新10条"
    - name: "SH-601318-latest5"
      params: ["601318", 1, 0, 5]
      description: "中国平安-最新5条"
    - name: "SH-600519-latest5"
      params: ["600519", 1, 0, 5]
      description: "贵州茅台-最新5条"
    - name: "SH-600036-latest10"
      params: ["600036", 1, 0, 10]
      description: "招商银行-最新10条"

  # 主板股票（深圳）测试
  shenzhen_main_board:
    - name: "SZ-000001-latest10"
      params: ["000001", 0, 0, 10]
      description: "平安银行-最新10条"
    - name: "SZ-000002-latest5"
      params: ["000002", 0, 0, 5]
      description: "万科A-最新5条"
    - name: "SZ-002007-latest10"
      params: ["002007", 0, 0, 10]
      description: "华兰生物-最新10条"
    - name: "SZ-002415-latest5"
      params: ["002415", 0, 0, 5]
      description: "海康威视-最新5条"

  # 创业板股票测试
  gem_board:
    - name: "GEM-300059-latest10"
      params: ["300059", 0, 0, 10]
      description: "东方财富-最新10条"
    - name: "GEM-300750-latest5"
      params: ["300750", 0, 0, 5]
      description: "宁德时代-最新5条"
    - name: "GEM-300015-latest5"
      params: ["300015", 0, 0, 5]
      description: "爱尔眼科-最新5条"

  # 不同参数组合测试
  parameter_combination_tests:
    - name: "SZ-000725-offset5"
      params: ["000725", 0, 5, 15]
      description: "京东方A-测试offset偏移"
    - name: "SH-600276-offset10"
      params: ["600276", 1, 10, 20]
      description: "恒瑞医药-测试大offset偏移"

# 测试字段配置
test_fields:
  moneyflow_fields:
    - 'jcltime'
    - 'superIn'
    - 'superOut'
    - 'bigIn'
    - 'bigOut'
    - 'midIn'
    - 'midOut'
    - 'smallIn'
    - 'smallOut'

# 测试配置
test_config:
  method_name: 'get_moneyflow_req'
  required_attributes: ['amt', 'vol', 'cnt']
  data_types: ['amt', 'vol', 'cnt']
  
# 字段处理配置
field_processing:
  comparison_mode: 'exact'  # 精确比较模式
  check_data_length: true  # 检查数据长度一致性
  
# 接口说明
interface_info:
  description: "1805 单只股票历史资金流接口"
  msg_id: 1805
  parameters: "code, setcode, offset, num"
  response_structure: "amt, vol, cnt 三个数据集"
  notes: "每个数据集包含资金流向的9个字段，需要检查数据长度一致性"
