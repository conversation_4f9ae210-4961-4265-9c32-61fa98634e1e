# 股本变迁数据测试参数配置
# 格式: [nkey, setcode, code]

kline_test_params:
  # 深市股票测试场景 (setcode=0)
  shenzhen_stocks:
    - name: "SZ-000001-equity"
      params: [0, 0, "000001"]
      description: "深市主板-平安银行"
    - name: "SZ-300001-equity"
      params: [0, 0, "300001"]
      description: "深市创业板-特锐德"
    - name: "SZ-000002-equity"
      params: [0, 0, "000002"]
      description: "深市主板-万科A"
    - name: "SZ-002001-equity"
      params: [0, 0, "002001"]
      description: "深市中小板-新和成"
    - name: "SZ-300002-equity"
      params: [0, 0, "300002"]
      description: "深市创业板-神州泰岳"

  # 沪市股票测试场景 (setcode=1)
  shanghai_stocks:
    - name: "SH-600000-equity"
      params: [0, 1, "600000"]
      description: "沪市主板-浦发银行"
    - name: "SH-688001-equity"
      params: [0, 1, "688001"]
      description: "沪市科创板-华兴源创"
    - name: "SH-601398-equity"
      params: [0, 1, "601398"]
      description: "沪市主板-工商银行"
    - name: "SH-600519-equity"
      params: [0, 1, "600519"]
      description: "沪市主板-贵州茅台"
    - name: "SH-688002-equity"
      params: [0, 1, "688002"]
      description: "沪市科创板-睿创微纳"

# 测试字段配置
test_fields:
  equity_fields: ["Date", "Type", "V01", "V02", "V03", "V04"]

# 测试配置
test_config:
  method_name: 'get_equity_adjustments'
  decimal_places: 1
  
# 字段处理配置
field_processing:
  decimal_places: 1  # 浮点数字段保留小数位数
