# 多股票行情测试参数配置 (1728)
# 格式: [nkey, setcode, code]

kline_test_params:
  # 深市股票测试
  shenzhen_stocks:
    - name: "SZ-000001-bank"
      params: [0, 0, "000001"]
      description: "深市-平安银行"
    - name: "SZ-300115-precision"
      params: [0, 0, "300115"]
      description: "深市-长盈精密"
    - name: "SZ-302132-tech"
      params: [0, 0, "302132"]
      description: "深市-龙源技术"
    - name: "SZ-000628-development"
      params: [0, 0, "000628"]
      description: "深市-高新发展"

  # 沪市股票测试
  shanghai_stocks:
    - name: "SH-600000-bank"
      params: [0, 1, "600000"]
      description: "沪市-浦发银行"
    - name: "SH-601318-insurance"
      params: [0, 1, "601318"]
      description: "沪市-中国平安"
    - name: "SH-600036-bank"
      params: [0, 1, "600036"]
      description: "沪市-招商银行"

  # 北交所股票测试
  beijing_stocks:
    - name: "BJ-830001-stock"
      params: [0, 2, "830001"]
      description: "北交所-830001"

  # 中金所期货测试
  cffex_futures:
    - name: "CFFEX-IC2509-main"
      params: [0, 3, "IC2509"]
      description: "中金所-IC2509"
    - name: "CFFEX-ICL8-continuous"
      params: [0, 3, "ICL8"]
      description: "中金所-ICL8"
    - name: "CFFEX-ICL9-continuous"
      params: [0, 3, "ICL9"]
      description: "中金所-ICL9"

# 测试字段配置
test_fields:
  hq_fields:
    - 'Status'
    - 'PreClosePrice'
    - 'OpenPrice'
    - 'HighPrice'
    - 'LowPrice'
    - 'NowPrice'
    - 'Volume'
    - 'Amount'
    - 'BuyPrice'
    - 'SellPrice'
    - 'BuyVolume'
    - 'SellVolume'
  
  excluded_fields:
    - 'nkey'
    - 'TickCount'

# 测试配置
test_config:
  method_name: 'get_multi_hq'
  response_attribute: 'ahq'
  assert_mode: 'batch'
  
# 字段处理配置
field_processing:
  comparison_mode: 'exact'  # 精确比较模式
  exclude_nkey: true  # 排除nkey字段
  exclude_tickcount: true  # 排除TickCount字段
  
# 市场映射配置
market_mapping:
  0: "深市"
  1: "沪市"
  2: "北交所"
  3: "中金所"
  4: "上期所"
  5: "大商所"
  6: "郑商所"
  
# 接口说明
interface_info:
  description: "1728 多股票行情接口"
  msg_id: 1728
  parameters: "codes (包含nkey, setcode, code的字典列表)"
  response_structure: "ahq 行情数据"
  notes: "排除nkey和TickCount字段进行比较"
