[tool:pytest]
# 简化的pytest配置，禁用所有可能有问题的插件
addopts = -v --tb=short --ignore-glob=tests_backup_* -p no:anyio -p no:allure_pytest -p no:allure -p no:cacheprovider -p no:stepwise
testpaths = tests_ph tests_pz
# 确保从tests_ph和tests_pz目录收集测试，避免备份目录冲突
python_files = test_*.py
python_classes = Test*
python_functions = test_*
# 排除备份目录，避免重复导入冲突
norecursedirs = tests_backup_* __pycache__ .git .tox dist build *.egg

# 设置日志级别
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 标记
markers =
    concurrent: 并发测试标记
    slow: 慢速测试标记
